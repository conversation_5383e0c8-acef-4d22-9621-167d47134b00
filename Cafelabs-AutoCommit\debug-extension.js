"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.deactivate = exports.activate = void 0;
const vscode = require("vscode");
function activate(context) {
    console.log('DEBUG: AutoCommit extension activating...');
    // Show immediate feedback
    vscode.window.showInformationMessage('AutoCommit Debug: Extension activated!');
    // Register a simple command first
    const disposable = vscode.commands.registerCommand('autocommit.test', () => {
        vscode.window.showInformationMessage('AutoCommit test command works!');
    });
    context.subscriptions.push(disposable);
    // Try to register the webview provider
    try {
        const provider = new DebugAutoCommitProvider(context);
        context.subscriptions.push(vscode.window.registerWebviewViewProvider('autocommitSettings', provider, {
            webviewOptions: {
                retainContextWhenHidden: true
            }
        }));
        console.log('DEBUG: Webview provider registered successfully');
        vscode.window.showInformationMessage('AutoCommit: Webview provider registered');
    }
    catch (error) {
        console.error('DEBUG: Error registering webview provider:', error);
        vscode.window.showErrorMessage(`AutoCommit: Failed to register webview - ${error}`);
    }
    console.log('DEBUG: AutoCommit extension activation complete');
}
exports.activate = activate;
class DebugAutoCommitProvider {
    constructor(context) {
        this.context = context;
    }
    resolveWebviewView(webviewView, context, _token) {
        console.log('DEBUG: resolveWebviewView called');
        vscode.window.showInformationMessage('AutoCommit: Webview resolving...');
        webviewView.webview.options = {
            enableScripts: true,
            localResourceRoots: [this.context.extensionUri]
        };
        webviewView.webview.html = this.getHtmlForWebview();
        console.log('DEBUG: Webview HTML set');
        vscode.window.showInformationMessage('AutoCommit: Webview HTML loaded');
    }
    getHtmlForWebview() {
        return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AutoCommit Debug</title>
    <style>
        body {
            font-family: var(--vscode-font-family);
            color: var(--vscode-foreground);
            background-color: var(--vscode-editor-background);
            padding: 10px;
        }
        .section {
            margin-bottom: 20px;
            padding: 10px;
            border: 1px solid var(--vscode-panel-border);
            border-radius: 4px;
        }
        button {
            background-color: var(--vscode-button-background);
            color: var(--vscode-button-foreground);
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        input[type="number"] {
            width: 60px;
            padding: 4px;
            border: 1px solid var(--vscode-input-border);
            background-color: var(--vscode-input-background);
            color: var(--vscode-input-foreground);
        }
    </style>
</head>
<body>
    <h2>🚀 AutoCommit Debug Panel</h2>
    
    <div class="section">
        <h3>✅ Plugin Status</h3>
        <p>If you can see this, the plugin is working!</p>
        <button onclick="testFunction()">Test Button</button>
    </div>

    <div class="section">
        <h3>🕐 Scheduled Commit</h3>
        <label>
            <input type="checkbox" id="scheduledEnabled"> Enable scheduled commit
        </label>
        <br><br>
        <label>Time:</label>
        <input type="number" id="scheduledHour" min="0" max="23" value="18">:
        <input type="number" id="scheduledMinute" min="0" max="59" value="0">:
        <input type="number" id="scheduledSecond" min="0" max="59" value="0">
    </div>

    <div class="section">
        <h3>🔄 Interval Commit</h3>
        <label>
            <input type="checkbox" id="intervalEnabled"> Enable interval commit
        </label>
        <br><br>
        <label>Interval:</label>
        <input type="number" id="intervalHours" min="0" max="23" value="0">h
        <input type="number" id="intervalMinutes" min="0" max="59" value="30">m
        <input type="number" id="intervalSeconds" min="0" max="59" value="0">s
    </div>

    <div class="section">
        <h3>⚡ Controls</h3>
        <button onclick="enableAutoCommit()">Enable AutoCommit</button>
        <button onclick="disableAutoCommit()">Disable AutoCommit</button>
        <button onclick="saveSettings()">Save Settings</button>
    </div>

    <div id="status" style="margin-top: 10px; padding: 8px; background-color: var(--vscode-textBlockQuote-background);">
        Ready to configure AutoCommit settings
    </div>

    <script>
        function testFunction() {
            document.getElementById('status').textContent = 'Test button clicked!';
        }

        function saveSettings() {
            document.getElementById('status').textContent = 'Settings saved (debug mode)!';
        }

        function enableAutoCommit() {
            document.getElementById('status').textContent = 'AutoCommit enabled (debug mode)!';
        }

        function disableAutoCommit() {
            document.getElementById('status').textContent = 'AutoCommit disabled (debug mode)!';
        }
    </script>
</body>
</html>`;
    }
}
function deactivate() {
    console.log('DEBUG: AutoCommit extension deactivated');
}
exports.deactivate = deactivate;
//# sourceMappingURL=debug-extension.js.map