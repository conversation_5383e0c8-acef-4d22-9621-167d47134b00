# CafeLabs AutoCommit 演示指南

## 🎯 插件功能概览

CafeLabs AutoCommit 是一个VSCode插件，可以根据自定义的时间设置自动执行Git提交。

### 主要功能
- ✅ **定时提交**：每天在指定时间自动提交
- ✅ **间隔提交**：按设定间隔循环提交  
- ✅ **智能检测**：只在有变更时才提交
- ✅ **用户界面**：直观的侧边栏配置界面
- ✅ **手动控制**：随时启用/禁用功能

## 🚀 快速演示

### 步骤1：安装插件
```bash
# 方法1：从VSIX文件安装
# 在VSCode中：Ctrl+Shift+P → Extensions: Install from VSIX...
# 选择：cafelabs-autocommit-1.0.0.vsix

# 方法2：开发模式
# 在插件目录按F5启动扩展开发主机
```

### 步骤2：打开测试项目
```bash
# 运行测试脚本创建测试环境
node test-script.js

# 在VSCode中打开test-project文件夹
code test-project
```

### 步骤3：配置AutoCommit
1. 在左侧活动栏找到Git提交图标（AutoCommit）
2. 点击打开AutoCommit设置面板
3. 配置选项：
   - **定时提交**：勾选启用，设置时间（如：当前时间+2分钟）
   - **间隔提交**：勾选启用，设置间隔（如：0小时0分钟30秒）

### 步骤4：启用并测试
1. 点击"Enable AutoCommit"按钮
2. 修改test-project中的文件（如test.js）
3. 观察自动提交：
   - 间隔提交：每30秒检查并提交变更
   - 定时提交：在设定时间执行提交

## 📋 测试场景

### 场景1：间隔提交测试
```bash
# 1. 设置间隔提交：30秒
# 2. 启用AutoCommit
# 3. 修改test.js文件
# 4. 等待30秒观察自动提交
# 5. 再次修改文件，观察下一次提交
```

### 场景2：定时提交测试
```bash
# 1. 设置定时提交：当前时间+2分钟
# 2. 启用AutoCommit
# 3. 修改文件
# 4. 等待到设定时间观察提交
```

### 场景3：混合模式测试
```bash
# 1. 同时启用定时和间隔提交
# 2. 观察两种模式的协同工作
```

## 🔍 验证结果

### 检查Git日志
```bash
# 在test-project目录下
git log --oneline -10

# 应该看到类似输出：
# abc1234 Auto-commit: 2024-06-23 15:30:45
# def5678 Auto-commit: 2024-06-23 15:30:15
```

### 检查VSCode输出
1. 打开VSCode输出面板（Ctrl+Shift+U）
2. 选择"CafeLabs AutoCommit"输出通道
3. 查看插件运行日志

## 🎨 界面功能

### 侧边栏界面
- **定时提交区域**：
  - 启用复选框
  - 时:分:秒输入框
- **间隔提交区域**：
  - 启用复选框  
  - 小时:分钟:秒输入框
- **控制按钮**：
  - Enable AutoCommit
  - Disable AutoCommit
  - Save Settings

### 状态显示
- 实时显示当前设置状态
- 提交成功/失败消息
- 下次提交时间预告

## 🛠️ 高级功能

### 自定义提交消息
- 默认格式：`Auto-commit: YYYY-MM-DD HH:mm:ss`
- 包含时间戳便于追踪

### 智能检测
- 只在有未提交变更时执行提交
- 自动检测Git仓库状态
- 跳过空提交

### 错误处理
- Git仓库检查
- 权限验证
- 网络连接处理

## 📊 性能特点

- **轻量级**：插件大小仅10KB
- **低资源占用**：后台运行几乎无性能影响
- **可靠性**：完善的错误处理机制
- **兼容性**：支持VSCode 1.74.0+

## 🔧 故障排除

### 常见问题
1. **插件不显示**：检查是否正确安装并重启VSCode
2. **自动提交不工作**：确保是Git仓库且有未提交变更
3. **权限错误**：检查Git配置和仓库权限

### 调试模式
```bash
# 开发者模式下查看控制台
# Ctrl+Shift+I → Console
# 查看详细错误信息
```

## 📝 总结

CafeLabs AutoCommit 插件成功实现了：
- ✅ 左侧栏图标和界面
- ✅ 定时和间隔提交功能
- ✅ 时分秒精确设置
- ✅ 多种模式可同时启用
- ✅ Git仓库智能检测
- ✅ 完整的测试验证

插件已准备就绪，可以投入使用！
