{"version": 3, "file": "minimal-extension.js", "sourceRoot": "", "sources": ["../src/minimal-extension.ts"], "names": [], "mappings": ";;;AAAA,iCAAiC;AAEjC,iDAAyC;AAEzC,SAAgB,QAAQ,CAAC,OAAgC;IACrD,OAAO,CAAC,GAAG,CAAC,qCAAqC,CAAC,CAAC;IAEnD,SAAS;IACT,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,kCAAkC,CAAC,CAAC;IAEzE,SAAS;IACT,MAAM,WAAW,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,iBAAiB,EAAE,GAAG,EAAE;QACxE,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,kCAAkC,CAAC,CAAC;IAC7E,CAAC,CAAC,CAAC;IACH,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;IAExC,WAAW;IACX,MAAM,mBAAmB,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,yBAAyB,EAAE,KAAK,IAAI,EAAE;QAC9F,MAAM,mBAAmB,EAAE,CAAC;IAChC,CAAC,CAAC,CAAC;IACH,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;IAEhD,UAAU;IACV,MAAM,aAAa,GAAG,MAAM,CAAC,MAAM,CAAC,mBAAmB,CAAC,MAAM,CAAC,kBAAkB,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;IAC9F,aAAa,CAAC,IAAI,GAAG,0BAA0B,CAAC;IAChD,aAAa,CAAC,OAAO,GAAG,sBAAsB,CAAC;IAC/C,aAAa,CAAC,OAAO,GAAG,iBAAiB,CAAC;IAC1C,aAAa,CAAC,IAAI,EAAE,CAAC;IACrB,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;IAE1C,WAAW;IACX,IAAI;QACA,OAAO,CAAC,GAAG,CAAC,mCAAmC,CAAC,CAAC;QACjD,MAAM,QAAQ,GAAG,IAAI,mBAAmB,CAAC,OAAO,CAAC,CAAC;QAClD,MAAM,YAAY,GAAG,MAAM,CAAC,MAAM,CAAC,2BAA2B,CAC1D,oBAAoB,EACpB,QAAQ,EACR;YACI,cAAc,EAAE;gBACZ,uBAAuB,EAAE,IAAI;aAChC;SACJ,CACJ,CAAC;QACF,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QACzC,OAAO,CAAC,GAAG,CAAC,kCAAkC,CAAC,CAAC;QAChD,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,wCAAwC,CAAC,CAAC;KAClF;IAAC,OAAO,KAAK,EAAE;QACZ,OAAO,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAC;QACvD,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,0BAA0B,KAAK,EAAE,CAAC,CAAC;KACrE;IAED,OAAO;IACP,UAAU,CAAC,GAAG,EAAE;QACZ,OAAO,CAAC,GAAG,CAAC,uBAAuB,CAAC,CAAC;QACrC,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,+DAA+D,CAAC,CAAC;IAC1G,CAAC,EAAE,IAAI,CAAC,CAAC;IAET,OAAO,CAAC,GAAG,CAAC,gDAAgD,CAAC,CAAC;AAClE,CAAC;AAtDD,4BAsDC;AAED,MAAM,mBAAmB;IACrB,YAA6B,OAAgC;QAAhC,YAAO,GAAP,OAAO,CAAyB;IAAG,CAAC;IAE1D,kBAAkB,CACrB,WAA+B,EAC/B,OAAyC,EACzC,MAAgC;QAEhC,OAAO,CAAC,GAAG,CAAC,2BAA2B,CAAC,CAAC;QACzC,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,uCAAuC,CAAC,CAAC;QAE9E,WAAW,CAAC,OAAO,CAAC,OAAO,GAAG;YAC1B,aAAa,EAAE,IAAI;YACnB,kBAAkB,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC;SAClD,CAAC;QAEF,WAAW,CAAC,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;QAEjD,iBAAiB;QACjB,WAAW,CAAC,OAAO,CAAC,mBAAmB,CACnC,KAAK,EAAE,OAAO,EAAE,EAAE;YACd,OAAO,CAAC,GAAG,CAAC,gCAAgC,EAAE,OAAO,CAAC,CAAC;YACvD,QAAQ,OAAO,CAAC,OAAO,EAAE;gBACrB,KAAK,cAAc;oBACf,OAAO,CAAC,GAAG,CAAC,sCAAsC,CAAC,CAAC;oBACpD,MAAM,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,yBAAyB,CAAC,CAAC;oBAChE,MAAM;gBACV;oBACI,OAAO,CAAC,GAAG,CAAC,kBAAkB,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC;aACxD;QACL,CAAC,EACD,SAAS,EACT,IAAI,CAAC,OAAO,CAAC,aAAa,CAC7B,CAAC;QAEF,OAAO,CAAC,GAAG,CAAC,0BAA0B,CAAC,CAAC;QACxC,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,kCAAkC,CAAC,CAAC;IAC7E,CAAC;IAEO,cAAc;QAClB,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;QAgJP,CAAC;IACL,CAAC;CACJ;AAED,UAAU;AACV,KAAK,UAAU,mBAAmB;IAC9B,IAAI;QACA,OAAO,CAAC,GAAG,CAAC,gCAAgC,CAAC,CAAC;QAC9C,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,8BAA8B,CAAC,CAAC;QAErE,UAAU;QACV,MAAM,gBAAgB,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC;QAC3D,IAAI,CAAC,gBAAgB,IAAI,gBAAgB,CAAC,MAAM,KAAK,CAAC,EAAE;YACpD,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,6BAA6B,CAAC,CAAC;YAC9D,OAAO;SACV;QAED,MAAM,aAAa,GAAG,gBAAgB,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC;QACrD,OAAO,CAAC,GAAG,CAAC,iBAAiB,EAAE,aAAa,CAAC,CAAC;QAE9C,aAAa;QACb,IAAI;YACA,IAAA,wBAAQ,EAAC,yBAAyB,EAAE,EAAE,GAAG,EAAE,aAAa,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC,CAAC;SAC9E;QAAC,OAAO,KAAK,EAAE;YACZ,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,wBAAwB,CAAC,CAAC;YACzD,OAAO;SACV;QAED,UAAU;QACV,IAAI,MAAc,CAAC;QACnB,IAAI;YACA,MAAM,GAAG,IAAA,wBAAQ,EAAC,wBAAwB,EAAE;gBACxC,GAAG,EAAE,aAAa;gBAClB,QAAQ,EAAE,MAAM;gBAChB,KAAK,EAAE,MAAM;aAChB,CAAC,CAAC;SACN;QAAC,OAAO,KAAK,EAAE;YACZ,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,8BAA8B,CAAC,CAAC;YAC/D,OAAO;SACV;QAED,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,EAAE;YAChB,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,yBAAyB,CAAC,CAAC;YAChE,OAAO;SACV;QAED,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC;QAEnC,SAAS;QACT,IAAI;YACA,IAAA,wBAAQ,EAAC,WAAW,EAAE,EAAE,GAAG,EAAE,aAAa,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC,CAAC;YAC7D,OAAO,CAAC,GAAG,CAAC,wBAAwB,CAAC,CAAC;SACzC;QAAC,OAAO,KAAK,EAAE;YACZ,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,kCAAkC,CAAC,CAAC;YACnE,OAAO;SACV;QAED,SAAS;QACT,MAAM,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;QACjE,MAAM,aAAa,GAAG,gCAAgC,SAAS,EAAE,CAAC;QAElE,OAAO;QACP,IAAI;YACA,MAAM,MAAM,GAAG,IAAA,wBAAQ,EAAC,kBAAkB,aAAa,GAAG,EAAE;gBACxD,GAAG,EAAE,aAAa;gBAClB,QAAQ,EAAE,MAAM;gBAChB,KAAK,EAAE,MAAM;aAChB,CAAC,CAAC;YACH,OAAO,CAAC,GAAG,CAAC,gBAAgB,EAAE,MAAM,CAAC,CAAC;YAEtC,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,+BAA+B,aAAa,EAAE,CAAC,CAAC;SAExF;QAAC,OAAO,KAAK,EAAE;YACZ,OAAO,CAAC,KAAK,CAAC,eAAe,EAAE,KAAK,CAAC,CAAC;YACtC,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,4BAA4B,CAAC,CAAC;YAC7D,OAAO;SACV;KAEJ;IAAC,OAAO,KAAK,EAAE;QACZ,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;QACpD,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,2BAA2B,KAAK,EAAE,CAAC,CAAC;KACtE;AACL,CAAC;AAED,SAAgB,UAAU;IACtB,OAAO,CAAC,GAAG,CAAC,wCAAwC,CAAC,CAAC;AAC1D,CAAC;AAFD,gCAEC"}