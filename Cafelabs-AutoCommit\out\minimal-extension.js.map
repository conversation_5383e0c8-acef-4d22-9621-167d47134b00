{"version": 3, "file": "minimal-extension.js", "sourceRoot": "", "sources": ["../src/minimal-extension.ts"], "names": [], "mappings": ";;;AAAA,iCAAiC;AAEjC,SAAgB,QAAQ,CAAC,OAAgC;IACrD,OAAO,CAAC,GAAG,CAAC,qCAAqC,CAAC,CAAC;IAEnD,SAAS;IACT,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,kCAAkC,CAAC,CAAC;IAEzE,WAAW;IACX,MAAM,WAAW,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,iBAAiB,EAAE,GAAG,EAAE;QACxE,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,kCAAkC,CAAC,CAAC;IAC7E,CAAC,CAAC,CAAC;IACH,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;IAExC,UAAU;IACV,MAAM,aAAa,GAAG,MAAM,CAAC,MAAM,CAAC,mBAAmB,CAAC,MAAM,CAAC,kBAAkB,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;IAC9F,aAAa,CAAC,IAAI,GAAG,0BAA0B,CAAC;IAChD,aAAa,CAAC,OAAO,GAAG,sBAAsB,CAAC;IAC/C,aAAa,CAAC,OAAO,GAAG,iBAAiB,CAAC;IAC1C,aAAa,CAAC,IAAI,EAAE,CAAC;IACrB,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;IAE1C,WAAW;IACX,IAAI;QACA,OAAO,CAAC,GAAG,CAAC,mCAAmC,CAAC,CAAC;QACjD,MAAM,QAAQ,GAAG,IAAI,mBAAmB,CAAC,OAAO,CAAC,CAAC;QAClD,MAAM,YAAY,GAAG,MAAM,CAAC,MAAM,CAAC,2BAA2B,CAC1D,oBAAoB,EACpB,QAAQ,EACR;YACI,cAAc,EAAE;gBACZ,uBAAuB,EAAE,IAAI;aAChC;SACJ,CACJ,CAAC;QACF,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QACzC,OAAO,CAAC,GAAG,CAAC,kCAAkC,CAAC,CAAC;QAChD,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,wCAAwC,CAAC,CAAC;KAClF;IAAC,OAAO,KAAK,EAAE;QACZ,OAAO,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAC;QACvD,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,0BAA0B,KAAK,EAAE,CAAC,CAAC;KACrE;IAED,OAAO;IACP,UAAU,CAAC,GAAG,EAAE;QACZ,OAAO,CAAC,GAAG,CAAC,uBAAuB,CAAC,CAAC;QACrC,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,+DAA+D,CAAC,CAAC;IAC1G,CAAC,EAAE,IAAI,CAAC,CAAC;IAET,OAAO,CAAC,GAAG,CAAC,gDAAgD,CAAC,CAAC;AAClE,CAAC;AAhDD,4BAgDC;AAED,MAAM,mBAAmB;IACrB,YAA6B,OAAgC;QAAhC,YAAO,GAAP,OAAO,CAAyB;IAAG,CAAC;IAE1D,kBAAkB,CACrB,WAA+B,EAC/B,OAAyC,EACzC,MAAgC;QAEhC,OAAO,CAAC,GAAG,CAAC,2BAA2B,CAAC,CAAC;QACzC,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,uCAAuC,CAAC,CAAC;QAE9E,WAAW,CAAC,OAAO,CAAC,OAAO,GAAG;YAC1B,aAAa,EAAE,IAAI;YACnB,kBAAkB,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC;SAClD,CAAC;QAEF,WAAW,CAAC,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;QAEjD,OAAO,CAAC,GAAG,CAAC,0BAA0B,CAAC,CAAC;QACxC,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,kCAAkC,CAAC,CAAC;IAC7E,CAAC;IAEO,cAAc;QAClB,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;QA4HP,CAAC;IACL,CAAC;CACJ;AAED,SAAgB,UAAU;IACtB,OAAO,CAAC,GAAG,CAAC,wCAAwC,CAAC,CAAC;AAC1D,CAAC;AAFD,gCAEC"}