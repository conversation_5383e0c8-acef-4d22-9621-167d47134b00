import * as vscode from 'vscode';

export function activate(context: vscode.ExtensionContext) {
    console.log('=== MINIMAL AUTOCOMMIT STARTING ===');
    
    // 立即显示消息
    vscode.window.showInformationMessage('🚀 Minimal AutoCommit activated!');
    
    // 注册一个简单命令
    const testCommand = vscode.commands.registerCommand('autocommit.test', () => {
        vscode.window.showInformationMessage('✅ AutoCommit test command works!');
    });
    context.subscriptions.push(testCommand);
    
    // 注册状态栏项目
    const statusBarItem = vscode.window.createStatusBarItem(vscode.StatusBarAlignment.Right, 100);
    statusBarItem.text = "$(git-commit) AutoCommit";
    statusBarItem.tooltip = "AutoCommit is active";
    statusBarItem.command = 'autocommit.test';
    statusBarItem.show();
    context.subscriptions.push(statusBarItem);
    
    // 尝试注册视图容器
    try {
        console.log('=== REGISTERING VIEW PROVIDER ===');
        const provider = new MinimalViewProvider(context);
        const registration = vscode.window.registerWebviewViewProvider(
            'autocommitSettings', 
            provider,
            {
                webviewOptions: {
                    retainContextWhenHidden: true
                }
            }
        );
        context.subscriptions.push(registration);
        console.log('=== VIEW PROVIDER REGISTERED ===');
        vscode.window.showInformationMessage('📋 AutoCommit view provider registered');
    } catch (error) {
        console.error('=== ERROR REGISTERING VIEW ===', error);
        vscode.window.showErrorMessage(`AutoCommit view error: ${error}`);
    }
    
    // 延迟检查
    setTimeout(() => {
        console.log('=== DELAYED CHECK ===');
        vscode.window.showInformationMessage('🔍 AutoCommit delayed check - extension should be visible now');
    }, 2000);
    
    console.log('=== MINIMAL AUTOCOMMIT ACTIVATION COMPLETE ===');
}

class MinimalViewProvider implements vscode.WebviewViewProvider {
    constructor(private readonly context: vscode.ExtensionContext) {}

    public resolveWebviewView(
        webviewView: vscode.WebviewView,
        context: vscode.WebviewViewResolveContext,
        _token: vscode.CancellationToken,
    ) {
        console.log('=== WEBVIEW RESOLVING ===');
        vscode.window.showInformationMessage('🎯 AutoCommit webview is resolving...');
        
        webviewView.webview.options = {
            enableScripts: true,
            localResourceRoots: [this.context.extensionUri]
        };

        webviewView.webview.html = this.getMinimalHtml();
        
        console.log('=== WEBVIEW HTML SET ===');
        vscode.window.showInformationMessage('✅ AutoCommit webview HTML loaded');
    }

    private getMinimalHtml(): string {
        return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AutoCommit Minimal</title>
    <style>
        body {
            font-family: var(--vscode-font-family);
            color: var(--vscode-foreground);
            background-color: var(--vscode-editor-background);
            padding: 20px;
            text-align: center;
        }
        .success {
            color: #4CAF50;
            font-size: 18px;
            margin: 20px 0;
        }
        .section {
            margin: 20px 0;
            padding: 15px;
            border: 2px solid #4CAF50;
            border-radius: 8px;
            background-color: var(--vscode-textBlockQuote-background);
        }
        button {
            background-color: var(--vscode-button-background);
            color: var(--vscode-button-foreground);
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 10px;
            font-size: 14px;
        }
        button:hover {
            background-color: var(--vscode-button-hoverBackground);
        }
        input[type="number"] {
            width: 60px;
            padding: 8px;
            border: 1px solid var(--vscode-input-border);
            background-color: var(--vscode-input-background);
            color: var(--vscode-input-foreground);
            margin: 5px;
        }
        .checkbox-container {
            margin: 15px 0;
            text-align: left;
        }
    </style>
</head>
<body>
    <div class="success">🎉 AutoCommit Plugin Working!</div>
    
    <div class="section">
        <h2>🕐 Scheduled Commit</h2>
        <div class="checkbox-container">
            <label>
                <input type="checkbox" id="scheduledEnabled"> Enable scheduled commit
            </label>
        </div>
        <div>
            <label>Time:</label>
            <input type="number" id="scheduledHour" min="0" max="23" value="18" placeholder="HH">:
            <input type="number" id="scheduledMinute" min="0" max="59" value="0" placeholder="MM">:
            <input type="number" id="scheduledSecond" min="0" max="59" value="0" placeholder="SS">
        </div>
    </div>

    <div class="section">
        <h2>🔄 Interval Commit</h2>
        <div class="checkbox-container">
            <label>
                <input type="checkbox" id="intervalEnabled"> Enable interval commit
            </label>
        </div>
        <div>
            <label>Every:</label>
            <input type="number" id="intervalHours" min="0" max="23" value="0">h
            <input type="number" id="intervalMinutes" min="0" max="59" value="30">m
            <input type="number" id="intervalSeconds" min="0" max="59" value="0">s
        </div>
    </div>

    <div class="section">
        <h2>⚡ Controls</h2>
        <button onclick="enableAutoCommit()">Enable AutoCommit</button>
        <button onclick="disableAutoCommit()">Disable AutoCommit</button>
        <button onclick="testFunction()">Test Function</button>
    </div>

    <div id="status" style="margin-top: 20px; padding: 10px; background-color: var(--vscode-textBlockQuote-background); border-radius: 4px;">
        ✅ AutoCommit interface loaded successfully!
    </div>

    <script>
        console.log('AutoCommit webview script loaded');
        
        function testFunction() {
            document.getElementById('status').innerHTML = '🧪 Test button clicked at ' + new Date().toLocaleTimeString();
            console.log('Test function executed');
        }

        function enableAutoCommit() {
            const scheduled = document.getElementById('scheduledEnabled').checked;
            const interval = document.getElementById('intervalEnabled').checked;
            document.getElementById('status').innerHTML = 
                '✅ AutoCommit enabled! Scheduled: ' + scheduled + ', Interval: ' + interval;
        }

        function disableAutoCommit() {
            document.getElementById('status').innerHTML = '⏹️ AutoCommit disabled!';
        }

        // 自动显示当前时间
        setInterval(() => {
            const now = new Date();
            document.getElementById('status').innerHTML = 
                '🕐 Current time: ' + now.toLocaleTimeString() + ' - AutoCommit ready!';
        }, 5000);
    </script>
</body>
</html>`;
    }
}

export function deactivate() {
    console.log('=== MINIMAL AUTOCOMMIT DEACTIVATED ===');
}
