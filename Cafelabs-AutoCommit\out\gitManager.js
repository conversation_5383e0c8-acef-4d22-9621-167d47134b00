"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.GitManager = void 0;
const vscode = require("vscode");
const simple_git_1 = require("simple-git");
class GitManager {
    constructor() {
        this.git = null;
        this.workspaceRoot = null;
        this.initializeGit();
    }
    initializeGit() {
        if (vscode.workspace.workspaceFolders && vscode.workspace.workspaceFolders.length > 0) {
            this.workspaceRoot = vscode.workspace.workspaceFolders[0].uri.fsPath;
            this.git = (0, simple_git_1.simpleGit)(this.workspaceRoot);
        }
    }
    async checkGitRepository() {
        if (!this.git || !this.workspaceRoot) {
            return false;
        }
        try {
            await this.git.status();
            return true;
        }
        catch (error) {
            console.log('Not a git repository:', error);
            return false;
        }
    }
    async hasChanges() {
        if (!this.git) {
            return false;
        }
        try {
            const status = await this.git.status();
            return status.files.length > 0;
        }
        catch (error) {
            console.error('Error checking git status:', error);
            return false;
        }
    }
    async commitChanges(customMessage) {
        if (!this.git) {
            return { success: false, message: 'Git not initialized' };
        }
        try {
            const hasChanges = await this.hasChanges();
            if (!hasChanges) {
                return { success: false, message: 'No changes to commit' };
            }
            // Add all changes
            await this.git.add('.');
            // Generate commit message
            const message = customMessage || this.generateCommitMessage();
            // Commit changes
            const result = await this.git.commit(message);
            return {
                success: true,
                message: `Committed: ${message} (${result.commit})`
            };
        }
        catch (error) {
            console.error('Error committing changes:', error);
            return {
                success: false,
                message: `Commit failed: ${error}`
            };
        }
    }
    generateCommitMessage() {
        const now = new Date();
        const timestamp = now.toISOString().replace('T', ' ').substring(0, 19);
        return `Auto-commit: ${timestamp}`;
    }
    async getLastCommitInfo() {
        if (!this.git) {
            return 'Git not initialized';
        }
        try {
            const log = await this.git.log({ maxCount: 1 });
            if (log.latest) {
                const commit = log.latest;
                return `${commit.hash.substring(0, 7)} - ${commit.message} (${commit.date})`;
            }
            return 'No commits found';
        }
        catch (error) {
            return `Error: ${error}`;
        }
    }
}
exports.GitManager = GitManager;
//# sourceMappingURL=gitManager.js.map