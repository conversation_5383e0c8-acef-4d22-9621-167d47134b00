{"version": 3, "file": "timerManager.js", "sourceRoot": "", "sources": ["../src/timerManager.ts"], "names": [], "mappings": ";;;AAAA,iCAAiC;AAcjC,MAAa,YAAY;IASrB,YAAY,UAAsB;QAN1B,mBAAc,GAA0B,IAAI,CAAC;QAC7C,kBAAa,GAA0B,IAAI,CAAC;QAC5C,cAAS,GAAY,KAAK,CAAC;QAC3B,sBAAiB,GAAuC,IAAI,MAAM,CAAC,YAAY,EAAiB,CAAC;QACzF,wBAAmB,GAAgC,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC;QAG5F,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,IAAI,CAAC,QAAQ,GAAG;YACZ,gBAAgB,EAAE,KAAK;YACvB,aAAa,EAAE,CAAC;YAChB,eAAe,EAAE,CAAC;YAClB,eAAe,EAAE,CAAC;YAClB,eAAe,EAAE,KAAK;YACtB,aAAa,EAAE,CAAC;YAChB,eAAe,EAAE,CAAC;YAClB,eAAe,EAAE,CAAC;SACrB,CAAC;QACF,IAAI,CAAC,YAAY,EAAE,CAAC;IACxB,CAAC;IAEO,YAAY;QAChB,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,YAAY,CAAC,CAAC;QAC/D,IAAI,CAAC,QAAQ,GAAG;YACZ,gBAAgB,EAAE,MAAM,CAAC,GAAG,CAAC,kBAAkB,EAAE,KAAK,CAAC;YACvD,aAAa,EAAE,MAAM,CAAC,GAAG,CAAC,eAAe,EAAE,CAAC,CAAC;YAC7C,eAAe,EAAE,MAAM,CAAC,GAAG,CAAC,iBAAiB,EAAE,CAAC,CAAC;YACjD,eAAe,EAAE,MAAM,CAAC,GAAG,CAAC,iBAAiB,EAAE,CAAC,CAAC;YACjD,eAAe,EAAE,MAAM,CAAC,GAAG,CAAC,iBAAiB,EAAE,KAAK,CAAC;YACrD,aAAa,EAAE,MAAM,CAAC,GAAG,CAAC,eAAe,EAAE,CAAC,CAAC;YAC7C,eAAe,EAAE,MAAM,CAAC,GAAG,CAAC,iBAAiB,EAAE,CAAC,CAAC;YACjD,eAAe,EAAE,MAAM,CAAC,GAAG,CAAC,iBAAiB,EAAE,CAAC,CAAC;SACpD,CAAC;IACN,CAAC;IAEO,KAAK,CAAC,YAAY;QACtB,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,YAAY,CAAC,CAAC;QAC/D,MAAM,MAAM,CAAC,MAAM,CAAC,kBAAkB,EAAE,IAAI,CAAC,QAAQ,CAAC,gBAAgB,EAAE,MAAM,CAAC,mBAAmB,CAAC,SAAS,CAAC,CAAC;QAC9G,MAAM,MAAM,CAAC,MAAM,CAAC,eAAe,EAAE,IAAI,CAAC,QAAQ,CAAC,aAAa,EAAE,MAAM,CAAC,mBAAmB,CAAC,SAAS,CAAC,CAAC;QACxG,MAAM,MAAM,CAAC,MAAM,CAAC,iBAAiB,EAAE,IAAI,CAAC,QAAQ,CAAC,eAAe,EAAE,MAAM,CAAC,mBAAmB,CAAC,SAAS,CAAC,CAAC;QAC5G,MAAM,MAAM,CAAC,MAAM,CAAC,iBAAiB,EAAE,IAAI,CAAC,QAAQ,CAAC,eAAe,EAAE,MAAM,CAAC,mBAAmB,CAAC,SAAS,CAAC,CAAC;QAC5G,MAAM,MAAM,CAAC,MAAM,CAAC,iBAAiB,EAAE,IAAI,CAAC,QAAQ,CAAC,eAAe,EAAE,MAAM,CAAC,mBAAmB,CAAC,SAAS,CAAC,CAAC;QAC5G,MAAM,MAAM,CAAC,MAAM,CAAC,eAAe,EAAE,IAAI,CAAC,QAAQ,CAAC,aAAa,EAAE,MAAM,CAAC,mBAAmB,CAAC,SAAS,CAAC,CAAC;QACxG,MAAM,MAAM,CAAC,MAAM,CAAC,iBAAiB,EAAE,IAAI,CAAC,QAAQ,CAAC,eAAe,EAAE,MAAM,CAAC,mBAAmB,CAAC,SAAS,CAAC,CAAC;QAC5G,MAAM,MAAM,CAAC,MAAM,CAAC,iBAAiB,EAAE,IAAI,CAAC,QAAQ,CAAC,eAAe,EAAE,MAAM,CAAC,mBAAmB,CAAC,SAAS,CAAC,CAAC;QAE5G,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IAC/C,CAAC;IAED,cAAc,CAAC,WAAmC;QAC9C,IAAI,CAAC,QAAQ,GAAG,EAAE,GAAG,IAAI,CAAC,QAAQ,EAAE,GAAG,WAAW,EAAE,CAAC;QACrD,IAAI,CAAC,YAAY,EAAE,CAAC;QAEpB,IAAI,IAAI,CAAC,SAAS,EAAE;YAChB,IAAI,CAAC,aAAa,EAAE,CAAC;SACxB;IACL,CAAC;IAED,WAAW;QACP,OAAO,EAAE,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;IAChC,CAAC;IAED,gBAAgB;QACZ,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACtB,IAAI,CAAC,WAAW,EAAE,CAAC;IACvB,CAAC;IAED,iBAAiB;QACb,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QACvB,IAAI,CAAC,UAAU,EAAE,CAAC;IACtB,CAAC;IAEO,WAAW;QACf,IAAI,CAAC,UAAU,EAAE,CAAC;QAElB,IAAI,IAAI,CAAC,QAAQ,CAAC,gBAAgB,EAAE;YAChC,IAAI,CAAC,mBAAmB,EAAE,CAAC;SAC9B;QAED,IAAI,IAAI,CAAC,QAAQ,CAAC,eAAe,EAAE;YAC/B,IAAI,CAAC,kBAAkB,EAAE,CAAC;SAC7B;IACL,CAAC;IAEO,UAAU;QACd,IAAI,IAAI,CAAC,cAAc,EAAE;YACrB,YAAY,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YAClC,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;SAC9B;QAED,IAAI,IAAI,CAAC,aAAa,EAAE;YACpB,aAAa,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAClC,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;SAC7B;IACL,CAAC;IAEO,aAAa;QACjB,IAAI,IAAI,CAAC,SAAS,EAAE;YAChB,IAAI,CAAC,WAAW,EAAE,CAAC;SACtB;IACL,CAAC;IAEO,mBAAmB;QACvB,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;QACvB,MAAM,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;QAC7B,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,aAAa,EAAE,IAAI,CAAC,QAAQ,CAAC,eAAe,EAAE,IAAI,CAAC,QAAQ,CAAC,eAAe,EAAE,CAAC,CAAC,CAAC;QAEjH,gEAAgE;QAChE,IAAI,SAAS,IAAI,GAAG,EAAE;YAClB,SAAS,CAAC,OAAO,CAAC,SAAS,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC;SAC9C;QAED,MAAM,kBAAkB,GAAG,SAAS,CAAC,OAAO,EAAE,GAAG,GAAG,CAAC,OAAO,EAAE,CAAC;QAE/D,IAAI,CAAC,cAAc,GAAG,UAAU,CAAC,KAAK,IAAI,EAAE;YACxC,MAAM,IAAI,CAAC,aAAa,CAAC,kBAAkB,CAAC,CAAC;YAC7C,0BAA0B;YAC1B,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAC/B,CAAC,EAAE,kBAAkB,CAAC,CAAC;QAEvB,OAAO,CAAC,GAAG,CAAC,6BAA6B,SAAS,CAAC,cAAc,EAAE,EAAE,CAAC,CAAC;IAC3E,CAAC;IAEO,kBAAkB;QACtB,MAAM,UAAU,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,aAAa,GAAG,IAAI;YACpC,IAAI,CAAC,QAAQ,CAAC,eAAe,GAAG,EAAE;YAClC,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,GAAG,IAAI,CAAC;QAExD,IAAI,UAAU,GAAG,CAAC,EAAE;YAChB,IAAI,CAAC,aAAa,GAAG,WAAW,CAAC,KAAK,IAAI,EAAE;gBACxC,MAAM,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,CAAC;YAChD,CAAC,EAAE,UAAU,CAAC,CAAC;YAEf,OAAO,CAAC,GAAG,CAAC,iCAAiC,UAAU,GAAG,IAAI,UAAU,CAAC,CAAC;SAC7E;IACL,CAAC;IAEO,KAAK,CAAC,aAAa,CAAC,IAAY;QACpC,IAAI;YACA,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,kBAAkB,EAAE,CAAC;YAC7D,IAAI,CAAC,SAAS,EAAE;gBACZ,OAAO,CAAC,GAAG,CAAC,uCAAuC,CAAC,CAAC;gBACrD,OAAO;aACV;YAED,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,aAAa,EAAE,CAAC;YACrD,IAAI,MAAM,CAAC,OAAO,EAAE;gBAChB,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,GAAG,IAAI,KAAK,MAAM,CAAC,OAAO,EAAE,CAAC,CAAC;aACtE;iBAAM;gBACH,OAAO,CAAC,GAAG,CAAC,GAAG,IAAI,aAAa,MAAM,CAAC,OAAO,EAAE,CAAC,CAAC;aACrD;SACJ;QAAC,OAAO,KAAK,EAAE;YACZ,OAAO,CAAC,KAAK,CAAC,gBAAgB,IAAI,GAAG,EAAE,KAAK,CAAC,CAAC;SACjD;IACL,CAAC;IAED,OAAO;QACH,IAAI,CAAC,UAAU,EAAE,CAAC;QAClB,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,CAAC;IACrC,CAAC;CACJ;AAnKD,oCAmKC"}