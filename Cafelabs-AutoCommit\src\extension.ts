import * as vscode from 'vscode';
import { AutoCommitProvider } from './autoCommitProvider';
import { TimerManager } from './timerManager';
import { GitManager } from './gitManager';

let autoCommitProvider: AutoCommitProvider;
let timerManager: TimerManager;
let gitManager: GitManager;

export function activate(context: vscode.ExtensionContext) {
    console.log('CafeLabs AutoCommit extension is now active!');

    // Initialize managers
    gitManager = new GitManager();
    timerManager = new TimerManager(gitManager);
    autoCommitProvider = new AutoCommitProvider(context, timerManager);

    // Register the webview provider
    context.subscriptions.push(
        vscode.window.registerWebviewViewProvider('autocommitSettings', autoCommitProvider)
    );

    // Register commands
    context.subscriptions.push(
        vscode.commands.registerCommand('autocommit.enable', () => {
            timerManager.enableAutoCommit();
            vscode.window.showInformationMessage('AutoCommit enabled');
        })
    );

    context.subscriptions.push(
        vscode.commands.registerCommand('autocommit.disable', () => {
            timerManager.disableAutoCommit();
            vscode.window.showInformationMessage('AutoCommit disabled');
        })
    );

    context.subscriptions.push(
        vscode.commands.registerCommand('autocommit.manualCommit', async () => {
            const result = await gitManager.commitChanges();
            if (result.success) {
                vscode.window.showInformationMessage(`Manual commit successful: ${result.message}`);
            } else {
                vscode.window.showErrorMessage(`Manual commit failed: ${result.message}`);
            }
        })
    );

    // Auto-start if workspace is a git repository
    if (vscode.workspace.workspaceFolders) {
        gitManager.checkGitRepository().then(isGit => {
            if (isGit) {
                console.log('Git repository detected, AutoCommit ready');
            }
        });
    }
}

export function deactivate() {
    if (timerManager) {
        timerManager.dispose();
    }
}
