# AutoCommit Plugin Test Project

This is a test project to verify the AutoCommit VSCode extension functionality.

## How to Test

1. Open this folder in VSCode
2. Make sure this folder is a Git repository (run `git init` if needed)
3. Install and activate the AutoCommit extension
4. Look for the AutoCommit icon in the left sidebar
5. Configure the timing settings:
   - **Scheduled Commit**: Set a specific time for daily commits
   - **Interval Commit**: Set recurring intervals for commits
6. Enable AutoCommit
7. Make changes to files in this project
8. Observe automatic commits based on your settings

## Test Scenarios

### Test 1: Interval Commit
1. Enable interval commit with a short interval (e.g., 30 seconds)
2. Make changes to `test.js`
3. Wait for the interval to trigger an automatic commit

### Test 2: Scheduled Commit
1. Enable scheduled commit for a time 1-2 minutes in the future
2. Make changes to files
3. Wait for the scheduled time to see the automatic commit

### Test 3: Manual Commit
1. Use the manual commit command
2. Verify that changes are committed immediately

## Expected Behavior

- The extension should detect this as a Git repository
- Automatic commits should only happen when there are changes
- Commit messages should include timestamps
- The extension should show status messages for successful commits

## Files to Modify for Testing

- `test.js` - Main test file
- `README.md` - This file
- Create new files to test with multiple file changes
