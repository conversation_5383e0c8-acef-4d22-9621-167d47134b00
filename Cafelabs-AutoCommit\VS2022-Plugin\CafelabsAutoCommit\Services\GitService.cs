using System;
using System.IO;
using System.Linq;
using LibGit2Sharp;
using Microsoft.VisualStudio.Shell;

namespace CafelabsAutoCommit.Services
{
    /// <summary>
    /// Git操作服务
    /// </summary>
    public class GitService
    {
        private const string TARGET_BRANCH = "cafelabs-autoCommit";

        /// <summary>
        /// 执行双分支提交
        /// </summary>
        /// <param name="repositoryPath">仓库路径</param>
        /// <returns>提交结果</returns>
        public GitCommitResult PerformDualBranchCommit(string repositoryPath)
        {
            try
            {
                if (string.IsNullOrEmpty(repositoryPath) || !Directory.Exists(repositoryPath))
                {
                    return new GitCommitResult { Success = false, Message = "仓库路径无效" };
                }

                if (!Repository.IsValid(repositoryPath))
                {
                    return new GitCommitResult { Success = false, Message = "不是有效的Git仓库" };
                }

                using (var repo = new Repository(repositoryPath))
                {
                    // 检查是否有变更
                    var status = repo.RetrieveStatus();
                    if (!status.IsDirty)
                    {
                        return new GitCommitResult { Success = false, Message = "没有变更需要提交" };
                    }

                    // 获取当前分支
                    var currentBranch = repo.Head.FriendlyName;
                    
                    // 暂存所有变更
                    Commands.Stage(repo, "*");

                    // 生成提交消息
                    var timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
                    var commitMessage = $"AutoCommit: Manual commit at {timestamp}";

                    // 创建提交者信息
                    var signature = GetCommitSignature(repo);

                    // 1. 提交到当前分支
                    var commit = repo.Commit(commitMessage, signature, signature);

                    // 2. 提交到cafelabs-autoCommit分支
                    var targetBranchResult = CommitToTargetBranch(repo, currentBranch, commitMessage, signature);

                    var message = $"✅ 成功提交到 {currentBranch} 分支";
                    if (targetBranchResult.Success)
                    {
                        message += $"\n✅ 成功提交到 {TARGET_BRANCH} 分支";
                    }
                    else
                    {
                        message += $"\n⚠️ 提交到 {TARGET_BRANCH} 分支失败: {targetBranchResult.Message}";
                    }

                    return new GitCommitResult 
                    { 
                        Success = true, 
                        Message = message,
                        CommitId = commit.Sha,
                        CurrentBranch = currentBranch
                    };
                }
            }
            catch (Exception ex)
            {
                return new GitCommitResult 
                { 
                    Success = false, 
                    Message = $"提交失败: {ex.Message}" 
                };
            }
        }

        /// <summary>
        /// 提交到目标分支
        /// </summary>
        private GitCommitResult CommitToTargetBranch(Repository repo, string originalBranch, string commitMessage, Signature signature)
        {
            try
            {
                // 检查目标分支是否存在
                var targetBranch = repo.Branches[TARGET_BRANCH];
                
                if (targetBranch == null)
                {
                    // 创建新分支
                    targetBranch = repo.CreateBranch(TARGET_BRANCH);
                }

                // 切换到目标分支
                Commands.Checkout(repo, targetBranch);

                // 合并原分支的最新提交
                try
                {
                    var originalBranchRef = repo.Branches[originalBranch];
                    if (originalBranchRef != null)
                    {
                        var mergeResult = repo.Merge(originalBranchRef, signature);
                        
                        if (mergeResult.Status == MergeStatus.Conflicts)
                        {
                            // 如果有冲突，取消合并并返回错误
                            repo.Reset(ResetMode.Hard);
                            Commands.Checkout(repo, originalBranch);
                            return new GitCommitResult { Success = false, Message = "合并时发生冲突" };
                        }
                    }
                }
                catch (Exception mergeEx)
                {
                    // 合并失败，继续执行但记录错误
                    System.Diagnostics.Debug.WriteLine($"合并失败: {mergeEx.Message}");
                }

                // 切换回原分支
                Commands.Checkout(repo, originalBranch);

                return new GitCommitResult { Success = true, Message = "成功提交到目标分支" };
            }
            catch (Exception ex)
            {
                // 确保切换回原分支
                try
                {
                    Commands.Checkout(repo, originalBranch);
                }
                catch { }

                return new GitCommitResult { Success = false, Message = ex.Message };
            }
        }

        /// <summary>
        /// 获取提交者签名
        /// </summary>
        private Signature GetCommitSignature(Repository repo)
        {
            try
            {
                var config = repo.Config;
                var name = config.Get<string>("user.name")?.Value ?? "AutoCommit User";
                var email = config.Get<string>("user.email")?.Value ?? "<EMAIL>";
                return new Signature(name, email, DateTimeOffset.Now);
            }
            catch
            {
                return new Signature("AutoCommit User", "<EMAIL>", DateTimeOffset.Now);
            }
        }

        /// <summary>
        /// 获取仓库状态
        /// </summary>
        public GitStatusResult GetRepositoryStatus(string repositoryPath)
        {
            try
            {
                if (!Repository.IsValid(repositoryPath))
                {
                    return new GitStatusResult { IsValid = false, Message = "不是有效的Git仓库" };
                }

                using (var repo = new Repository(repositoryPath))
                {
                    var status = repo.RetrieveStatus();
                    var currentBranch = repo.Head.FriendlyName;
                    
                    return new GitStatusResult
                    {
                        IsValid = true,
                        CurrentBranch = currentBranch,
                        HasChanges = status.IsDirty,
                        AddedFiles = status.Added.Count(),
                        ModifiedFiles = status.Modified.Count(),
                        RemovedFiles = status.Removed.Count(),
                        UntrackedFiles = status.Untracked.Count()
                    };
                }
            }
            catch (Exception ex)
            {
                return new GitStatusResult { IsValid = false, Message = ex.Message };
            }
        }
    }

    /// <summary>
    /// Git提交结果
    /// </summary>
    public class GitCommitResult
    {
        public bool Success { get; set; }
        public string Message { get; set; }
        public string CommitId { get; set; }
        public string CurrentBranch { get; set; }
    }

    /// <summary>
    /// Git状态结果
    /// </summary>
    public class GitStatusResult
    {
        public bool IsValid { get; set; }
        public string Message { get; set; }
        public string CurrentBranch { get; set; }
        public bool HasChanges { get; set; }
        public int AddedFiles { get; set; }
        public int ModifiedFiles { get; set; }
        public int RemovedFiles { get; set; }
        public int UntrackedFiles { get; set; }
    }
}
