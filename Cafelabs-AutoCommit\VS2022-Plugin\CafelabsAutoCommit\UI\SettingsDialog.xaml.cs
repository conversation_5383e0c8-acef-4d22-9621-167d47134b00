using System;
using System.Windows;
using System.Windows.Controls;
using CafelabsAutoCommit.Models;
using CafelabsAutoCommit.Services;

namespace CafelabsAutoCommit.UI
{
    /// <summary>
    /// 设置对话框
    /// </summary>
    public partial class SettingsDialog : Window
    {
        private readonly SettingsService _settingsService;
        private AutoCommitSettings _originalSettings;
        private bool _hasUnsavedChanges = false;

        public SettingsDialog()
        {
            InitializeComponent();
            
            _settingsService = new SettingsService();
            
            // 加载当前设置
            LoadSettings();
            
            // 更新预览
            UpdatePreview();
        }

        #region 设置管理

        private void LoadSettings()
        {
            try
            {
                var settings = _settingsService.LoadSettings();
                _originalSettings = settings.Clone();
                
                // 更新UI控件
                ScheduledEnabledCheckBox.IsChecked = settings.ScheduledEnabled;
                ScheduledHourTextBox.Text = settings.ScheduledHour.ToString();
                ScheduledMinuteTextBox.Text = settings.ScheduledMinute.ToString();
                ScheduledSecondTextBox.Text = settings.ScheduledSecond.ToString();
                
                IntervalEnabledCheckBox.IsChecked = settings.IntervalEnabled;
                IntervalHoursTextBox.Text = settings.IntervalHours.ToString();
                IntervalMinutesTextBox.Text = settings.IntervalMinutes.ToString();
                IntervalSecondsTextBox.Text = settings.IntervalSeconds.ToString();
                
                StatusText.Text = "✅ 设置已加载";
                _hasUnsavedChanges = false;
            }
            catch (Exception ex)
            {
                StatusText.Text = $"❌ 加载设置失败: {ex.Message}";
            }
        }

        private AutoCommitSettings GetCurrentSettings()
        {
            return new AutoCommitSettings
            {
                ScheduledEnabled = ScheduledEnabledCheckBox.IsChecked ?? false,
                ScheduledHour = ParseInt(ScheduledHourTextBox.Text, 18),
                ScheduledMinute = ParseInt(ScheduledMinuteTextBox.Text, 0),
                ScheduledSecond = ParseInt(ScheduledSecondTextBox.Text, 0),
                
                IntervalEnabled = IntervalEnabledCheckBox.IsChecked ?? false,
                IntervalHours = ParseInt(IntervalHoursTextBox.Text, 0),
                IntervalMinutes = ParseInt(IntervalMinutesTextBox.Text, 30),
                IntervalSeconds = ParseInt(IntervalSecondsTextBox.Text, 0)
            };
        }

        private int ParseInt(string text, int defaultValue)
        {
            if (int.TryParse(text, out int result))
            {
                return Math.Max(0, result); // 确保非负数
            }
            return defaultValue;
        }

        private void UpdatePreview()
        {
            try
            {
                var settings = GetCurrentSettings();
                
                if (!settings.IsValid())
                {
                    PreviewText.Text = "⚠️ 当前设置无效，请检查时间格式";
                    return;
                }
                
                var preview = "预览: " + settings.GetDescription();
                
                if (settings.ScheduledEnabled)
                {
                    var nextScheduled = GetNextScheduledTime(settings.ScheduledTime);
                    preview += $"\n下次定时提交: {nextScheduled:yyyy-MM-dd HH:mm:ss}";
                }
                
                if (settings.IntervalEnabled)
                {
                    var intervalMs = settings.IntervalTime.TotalMilliseconds;
                    if (intervalMs < 300000) // 5分钟
                    {
                        preview += "\n⚠️ 警告: 间隔时间过短，建议不少于5分钟";
                    }
                }
                
                PreviewText.Text = preview;
            }
            catch (Exception ex)
            {
                PreviewText.Text = $"❌ 预览生成失败: {ex.Message}";
            }
        }

        private DateTime GetNextScheduledTime(TimeSpan scheduledTime)
        {
            var now = DateTime.Now;
            var today = now.Date.Add(scheduledTime);
            
            if (today <= now)
            {
                return today.AddDays(1); // 明天的指定时间
            }
            
            return today; // 今天的指定时间
        }

        #endregion

        #region 事件处理

        private void OnSettingChanged(object sender, RoutedEventArgs e)
        {
            _hasUnsavedChanges = true;
            StatusText.Text = "⚠️ 设置已修改，请保存更改";
            UpdatePreview();
        }

        private void OnSettingChanged(object sender, TextChangedEventArgs e)
        {
            _hasUnsavedChanges = true;
            StatusText.Text = "⚠️ 设置已修改，请保存更改";
            UpdatePreview();
        }

        private void OnSaveClick(object sender, RoutedEventArgs e)
        {
            try
            {
                var settings = GetCurrentSettings();
                
                if (!settings.IsValid())
                {
                    MessageBox.Show("设置值无效，请检查时间格式。\n\n" +
                                  "时间范围:\n" +
                                  "• 小时: 0-23\n" +
                                  "• 分钟: 0-59\n" +
                                  "• 秒: 0-59", 
                                  "设置错误", 
                                  MessageBoxButton.OK, 
                                  MessageBoxImage.Warning);
                    return;
                }
                
                _settingsService.SaveSettings(settings);
                _originalSettings = settings.Clone();
                _hasUnsavedChanges = false;
                
                StatusText.Text = "✅ 设置已保存成功";
                
                MessageBox.Show("设置已保存成功！\n\n" + settings.GetDescription(), 
                              "保存成功", 
                              MessageBoxButton.OK, 
                              MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                StatusText.Text = $"❌ 保存失败: {ex.Message}";
                MessageBox.Show($"保存设置失败:\n{ex.Message}", 
                              "保存错误", 
                              MessageBoxButton.OK, 
                              MessageBoxImage.Error);
            }
        }

        private void OnCancelClick(object sender, RoutedEventArgs e)
        {
            if (_hasUnsavedChanges)
            {
                var result = MessageBox.Show("有未保存的更改，确定要取消吗？", 
                                           "确认取消", 
                                           MessageBoxButton.YesNo, 
                                           MessageBoxImage.Question);
                if (result == MessageBoxResult.No)
                {
                    return;
                }
            }
            
            DialogResult = false;
            Close();
        }

        private void OnResetClick(object sender, RoutedEventArgs e)
        {
            var result = MessageBox.Show("确定要重置所有设置为默认值吗？", 
                                       "确认重置", 
                                       MessageBoxButton.YesNo, 
                                       MessageBoxImage.Question);
            
            if (result == MessageBoxResult.Yes)
            {
                try
                {
                    var defaultSettings = new AutoCommitSettings();
                    
                    ScheduledEnabledCheckBox.IsChecked = defaultSettings.ScheduledEnabled;
                    ScheduledHourTextBox.Text = defaultSettings.ScheduledHour.ToString();
                    ScheduledMinuteTextBox.Text = defaultSettings.ScheduledMinute.ToString();
                    ScheduledSecondTextBox.Text = defaultSettings.ScheduledSecond.ToString();
                    
                    IntervalEnabledCheckBox.IsChecked = defaultSettings.IntervalEnabled;
                    IntervalHoursTextBox.Text = defaultSettings.IntervalHours.ToString();
                    IntervalMinutesTextBox.Text = defaultSettings.IntervalMinutes.ToString();
                    IntervalSecondsTextBox.Text = defaultSettings.IntervalSeconds.ToString();
                    
                    _hasUnsavedChanges = true;
                    StatusText.Text = "🔄 已重置为默认设置，请保存更改";
                    UpdatePreview();
                }
                catch (Exception ex)
                {
                    StatusText.Text = $"❌ 重置失败: {ex.Message}";
                }
            }
        }

        private void OnTestClick(object sender, RoutedEventArgs e)
        {
            try
            {
                var settings = GetCurrentSettings();
                var testResult = $"🧪 测试结果 - {DateTime.Now:HH:mm:ss}\n\n";
                testResult += $"设置有效性: {(settings.IsValid() ? "✅ 有效" : "❌ 无效")}\n";
                testResult += $"设置描述: {settings.GetDescription()}\n";
                
                if (settings.ScheduledEnabled)
                {
                    var nextTime = GetNextScheduledTime(settings.ScheduledTime);
                    testResult += $"下次定时提交: {nextTime:yyyy-MM-dd HH:mm:ss}\n";
                }
                
                if (settings.IntervalEnabled)
                {
                    testResult += $"间隔时间: {settings.IntervalTime}\n";
                }
                
                MessageBox.Show(testResult, "测试结果", MessageBoxButton.OK, MessageBoxImage.Information);
                StatusText.Text = "🧪 测试完成";
            }
            catch (Exception ex)
            {
                StatusText.Text = $"❌ 测试失败: {ex.Message}";
            }
        }

        #endregion

        #region 窗口事件

        protected override void OnClosing(System.ComponentModel.CancelEventArgs e)
        {
            if (_hasUnsavedChanges)
            {
                var result = MessageBox.Show("有未保存的更改，确定要关闭吗？", 
                                           "确认关闭", 
                                           MessageBoxButton.YesNo, 
                                           MessageBoxImage.Question);
                if (result == MessageBoxResult.No)
                {
                    e.Cancel = true;
                    return;
                }
            }
            
            base.OnClosing(e);
        }

        #endregion
    }
}
