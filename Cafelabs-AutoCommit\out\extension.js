"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.deactivate = exports.activate = void 0;
const vscode = require("vscode");
const autoCommitProvider_1 = require("./autoCommitProvider");
const timerManager_1 = require("./timerManager");
const gitManager_1 = require("./gitManager");
let autoCommitProvider;
let timerManager;
let gitManager;
function activate(context) {
    console.log('CafeLabs AutoCommit extension is now active!');
    // Initialize managers
    gitManager = new gitManager_1.GitManager();
    timerManager = new timerManager_1.TimerManager(gitManager);
    autoCommitProvider = new autoCommitProvider_1.AutoCommitProvider(context, timerManager);
    // Register the webview provider
    context.subscriptions.push(vscode.window.registerWebviewViewProvider('autocommitSettings', autoCommitProvider));
    // Register commands
    context.subscriptions.push(vscode.commands.registerCommand('autocommit.enable', () => {
        timerManager.enableAutoCommit();
        vscode.window.showInformationMessage('AutoCommit enabled');
    }));
    context.subscriptions.push(vscode.commands.registerCommand('autocommit.disable', () => {
        timerManager.disableAutoCommit();
        vscode.window.showInformationMessage('AutoCommit disabled');
    }));
    context.subscriptions.push(vscode.commands.registerCommand('autocommit.manualCommit', async () => {
        const result = await gitManager.commitChanges();
        if (result.success) {
            vscode.window.showInformationMessage(`Manual commit successful: ${result.message}`);
        }
        else {
            vscode.window.showErrorMessage(`Manual commit failed: ${result.message}`);
        }
    }));
    // Auto-start if workspace is a git repository
    if (vscode.workspace.workspaceFolders) {
        gitManager.checkGitRepository().then(isGit => {
            if (isGit) {
                console.log('Git repository detected, AutoCommit ready');
            }
        });
    }
}
exports.activate = activate;
function deactivate() {
    if (timerManager) {
        timerManager.dispose();
    }
}
exports.deactivate = deactivate;
//# sourceMappingURL=extension.js.map