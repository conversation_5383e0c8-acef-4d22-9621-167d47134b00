{"version": 3, "file": "autoCommitProvider.js", "sourceRoot": "", "sources": ["../src/autoCommitProvider.ts"], "names": [], "mappings": ";;;AAAA,iCAAiC;AAGjC,MAAa,kBAAkB;IAK3B,YACqB,iBAA0C,EAC3D,YAA0B;QADT,sBAAiB,GAAjB,iBAAiB,CAAyB;QAG3D,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;QAEjC,8BAA8B;QAC9B,IAAI,CAAC,YAAY,CAAC,mBAAmB,CAAC,GAAG,EAAE;YACvC,IAAI,CAAC,aAAa,EAAE,CAAC;QACzB,CAAC,CAAC,CAAC;IACP,CAAC;IAEM,kBAAkB,CACrB,WAA+B,EAC/B,OAAyC,EACzC,MAAgC;QAEhC,IAAI,CAAC,KAAK,GAAG,WAAW,CAAC;QAEzB,WAAW,CAAC,OAAO,CAAC,OAAO,GAAG;YAC1B,aAAa,EAAE,IAAI;YACnB,kBAAkB,EAAE;gBAChB,IAAI,CAAC,iBAAiB,CAAC,YAAY;aACtC;SACJ,CAAC;QAEF,OAAO,CAAC,GAAG,CAAC,iCAAiC,CAAC,CAAC;QAC/C,WAAW,CAAC,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,kBAAkB,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;QACxE,OAAO,CAAC,GAAG,CAAC,6BAA6B,CAAC,CAAC;QAE3C,mCAAmC;QACnC,WAAW,CAAC,OAAO,CAAC,mBAAmB,CACnC,OAAO,CAAC,EAAE;YACN,QAAQ,OAAO,CAAC,OAAO,EAAE;gBACrB,KAAK,gBAAgB;oBACjB,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;oBACnD,MAAM;gBACV,KAAK,kBAAkB;oBACnB,IAAI,CAAC,YAAY,CAAC,gBAAgB,EAAE,CAAC;oBACrC,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,oBAAoB,CAAC,CAAC;oBAC3D,MAAM;gBACV,KAAK,mBAAmB;oBACpB,IAAI,CAAC,YAAY,CAAC,iBAAiB,EAAE,CAAC;oBACtC,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,qBAAqB,CAAC,CAAC;oBAC5D,MAAM;aACb;QACL,CAAC,EACD,SAAS,EACT,IAAI,CAAC,iBAAiB,CAAC,aAAa,CACvC,CAAC;QAEF,IAAI,CAAC,aAAa,EAAE,CAAC;IACzB,CAAC;IAEO,aAAa;QACjB,IAAI,IAAI,CAAC,KAAK,EAAE;YACZ,MAAM,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE,CAAC;YACjD,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC;gBAC3B,OAAO,EAAE,gBAAgB;gBACzB,QAAQ,EAAE,QAAQ;aACrB,CAAC,CAAC;SACN;IACL,CAAC;IAEO,kBAAkB,CAAC,OAAuB;QAC9C,MAAM,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE,CAAC;QACjD,OAAO,CAAC,GAAG,CAAC,gCAAgC,EAAE,QAAQ,CAAC,CAAC;QAExD,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;+DA2EgD,QAAQ,CAAC,gBAAgB,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE;;;;;;;kFAOvB,QAAQ,CAAC,aAAa;;oFAEpB,QAAQ,CAAC,eAAe;;oFAExB,QAAQ,CAAC,eAAe;;;;;;;;;8DAS9C,QAAQ,CAAC,eAAe,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE;;;;;;;kFAOrB,QAAQ,CAAC,aAAa;;oFAEpB,QAAQ,CAAC,eAAe;;oFAExB,QAAQ,CAAC,eAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;QAwEpG,CAAC;IACL,CAAC;;AA5PL,gDA6PC;AA5P0B,2BAAQ,GAAG,oBAAoB,CAAC"}