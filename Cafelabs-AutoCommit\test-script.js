#!/usr/bin/env node

/**
 * Test script to verify AutoCommit plugin functionality
 * This script simulates file changes and tests the plugin behavior
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

const TEST_PROJECT_DIR = path.join(__dirname, 'test-project');
const TEST_FILE = path.join(TEST_PROJECT_DIR, 'test.js');

console.log('🧪 AutoCommit Plugin Test Script');
console.log('================================');

function initializeTestProject() {
    console.log('\n📁 Initializing test project...');
    
    // Ensure test project directory exists
    if (!fs.existsSync(TEST_PROJECT_DIR)) {
        fs.mkdirSync(TEST_PROJECT_DIR, { recursive: true });
    }
    
    // Initialize git repository if not exists
    try {
        process.chdir(TEST_PROJECT_DIR);
        execSync('git status', { stdio: 'ignore' });
        console.log('✅ Git repository already exists');
    } catch (error) {
        console.log('🔧 Initializing Git repository...');
        execSync('git init');
        execSync('git config user.name "AutoCommit Test"');
        execSync('git config user.email "<EMAIL>"');
        console.log('✅ Git repository initialized');
    }
    
    // Create initial commit if no commits exist
    try {
        execSync('git log --oneline -1', { stdio: 'ignore' });
        console.log('✅ Git history exists');
    } catch (error) {
        console.log('🔧 Creating initial commit...');
        execSync('git add .');
        execSync('git commit -m "Initial commit for AutoCommit testing"');
        console.log('✅ Initial commit created');
    }
}

function simulateFileChanges() {
    console.log('\n📝 Simulating file changes...');
    
    const timestamp = new Date().toISOString();
    const testContent = `// Test file modified at ${timestamp}
console.log('AutoCommit test - File modified at: ${timestamp}');

const testData = {
    lastModified: '${timestamp}',
    testNumber: ${Math.floor(Math.random() * 1000)},
    message: 'Testing AutoCommit functionality'
};

function getCurrentTime() {
    return new Date().toISOString();
}

console.log('Test data:', testData);
module.exports = { testData, getCurrentTime };
`;
    
    fs.writeFileSync(TEST_FILE, testContent);
    console.log(`✅ Modified ${TEST_FILE}`);
    
    // Create additional test file
    const additionalFile = path.join(TEST_PROJECT_DIR, `test-${Date.now()}.txt`);
    fs.writeFileSync(additionalFile, `Test file created at ${timestamp}\nThis file tests multiple file changes.`);
    console.log(`✅ Created ${additionalFile}`);
}

function checkGitStatus() {
    console.log('\n📊 Checking Git status...');
    
    try {
        const status = execSync('git status --porcelain', { encoding: 'utf8' });
        if (status.trim()) {
            console.log('📋 Uncommitted changes detected:');
            console.log(status);
            return true;
        } else {
            console.log('✅ No uncommitted changes');
            return false;
        }
    } catch (error) {
        console.error('❌ Error checking Git status:', error.message);
        return false;
    }
}

function showGitLog() {
    console.log('\n📜 Recent Git commits:');
    
    try {
        const log = execSync('git log --oneline -5', { encoding: 'utf8' });
        console.log(log);
    } catch (error) {
        console.error('❌ Error reading Git log:', error.message);
    }
}

function runTests() {
    console.log('\n🚀 Running AutoCommit tests...');
    
    // Test 1: Initialize project
    initializeTestProject();
    
    // Test 2: Check initial status
    checkGitStatus();
    showGitLog();
    
    // Test 3: Simulate changes
    simulateFileChanges();
    
    // Test 4: Check status after changes
    const hasChanges = checkGitStatus();
    
    if (hasChanges) {
        console.log('\n✅ Test setup complete! The AutoCommit plugin should now detect these changes.');
        console.log('\n📋 Next steps:');
        console.log('1. Open the test-project folder in VSCode');
        console.log('2. Install and activate the AutoCommit extension');
        console.log('3. Configure the timing settings in the AutoCommit sidebar');
        console.log('4. Enable AutoCommit and observe automatic commits');
        console.log('5. Run this script again to create more test changes');
    } else {
        console.log('\n⚠️  No changes detected. The test may not work as expected.');
    }
    
    console.log('\n🔍 Monitor the VSCode output and Git log to verify AutoCommit functionality.');
}

// Run the tests
runTests();
