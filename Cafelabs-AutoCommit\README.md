# CafeLabs AutoCommit

A VSCode extension that automatically commits your Git changes based on customizable timing settings.

## Features

- 🕐 **Scheduled Commits**: Set a specific time for daily automatic commits
- 🔄 **Interval Commits**: Configure recurring intervals for automatic commits  
- ⚡ **Manual Control**: Enable/disable AutoCommit and trigger manual commits
- 🎯 **Smart Detection**: Only commits when there are actual changes
- 📊 **Git Integration**: Seamlessly works with your existing Git workflow

## Installation

### From VSIX Package

1. Download the `.vsix` file
2. Open VSCode
3. Go to Extensions view (`Ctrl+Shift+X`)
4. Click the `...` menu and select "Install from VSIX..."
5. Select the downloaded `.vsix` file

### From Source

1. Clone this repository
2. Run `npm install` to install dependencies
3. Run `npm run compile` to build the extension
4. Press `F5` to open a new Extension Development Host window

## Usage

1. **Open the AutoCommit Panel**: Look for the Git commit icon in the left sidebar
2. **Configure Settings**:
   - **Scheduled Commit**: Enable and set hour:minute:second for daily commits
   - **Interval Commit**: Enable and set hours:minutes:seconds for recurring commits
3. **Enable AutoCommit**: Click the "Enable AutoCommit" button
4. **Monitor**: The extension will automatically commit changes based on your settings

## Settings

The extension provides the following configuration options:

- `autocommit.scheduledEnabled`: Enable/disable scheduled commits
- `autocommit.scheduledHour`: Hour for scheduled commit (0-23)
- `autocommit.scheduledMinute`: Minute for scheduled commit (0-59)
- `autocommit.scheduledSecond`: Second for scheduled commit (0-59)
- `autocommit.intervalEnabled`: Enable/disable interval commits
- `autocommit.intervalHours`: Hours for interval commits
- `autocommit.intervalMinutes`: Minutes for interval commits
- `autocommit.intervalSeconds`: Seconds for interval commits

## Commands

- `autocommit.enable`: Enable AutoCommit
- `autocommit.disable`: Disable AutoCommit
- `autocommit.manualCommit`: Trigger a manual commit

## Requirements

- VSCode 1.74.0 or higher
- Git repository in your workspace
- Node.js for development

## Development

### Building

```bash
npm install
npm run compile
```

### Testing

1. Run the test script to set up a test environment:
   ```bash
   node test-script.js
   ```

2. Open the `test-project` folder in VSCode
3. Install the extension and test the functionality

### Packaging

```bash
npm install -g vsce
vsce package
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

MIT License - see LICENSE file for details

## Changelog

### 1.0.0
- Initial release
- Scheduled and interval commit functionality
- Webview-based settings interface
- Git integration with change detection
