<?xml version="1.0" encoding="utf-8"?>
<PackageManifest Version="2.0.0" xmlns="http://schemas.microsoft.com/developer/vsx-schema/2011" xmlns:d="http://schemas.microsoft.com/developer/vsx-schema-design/2011">
    <Metadata>
        <Identity Id="CafelabsAutoCommit.A7C02A2B-24E4-4F50-9D12-7E3F8B2C4D5E" Version="1.0.0" Language="en-US" Publisher="CafeLabs" />
        <DisplayName>CafeLabs AutoCommit</DisplayName>
        <Description xml:space="preserve">Automatic Git commits with dual branch support for Visual Studio 2022. Features scheduled commits, interval commits, and manual dual-branch commits to current branch and cafelabs-autoCommit branch.</Description>
        <MoreInfo>https://github.com/cafelabs/autocommit-vs2022</MoreInfo>
        <License>LICENSE.txt</License>
        <GettingStartedGuide>https://github.com/cafelabs/autocommit-vs2022/blob/main/README.md</GettingStartedGuide>
        <Icon>Resources\AutoCommitPackage.ico</Icon>
        <PreviewImage>Resources\AutoCommitCommand.png</PreviewImage>
        <Tags>git, autocommit, automation, dual-branch, cafelabs</Tags>
    </Metadata>
    <Installation>
        <InstallationTarget Id="Microsoft.VisualStudio.Community" Version="[17.0, 18.0)">
            <ProductArchitecture>amd64</ProductArchitecture>
        </InstallationTarget>
        <InstallationTarget Id="Microsoft.VisualStudio.Pro" Version="[17.0, 18.0)">
            <ProductArchitecture>amd64</ProductArchitecture>
        </InstallationTarget>
        <InstallationTarget Id="Microsoft.VisualStudio.Enterprise" Version="[17.0, 18.0)">
            <ProductArchitecture>amd64</ProductArchitecture>
        </InstallationTarget>
    </Installation>
    <Dependencies>
        <Dependency Id="Microsoft.Framework.NDP" DisplayName="Microsoft .NET Framework" d:Source="Manual" Version="[4.8,)" />
    </Dependencies>
    <Prerequisites>
        <Prerequisite Id="Microsoft.VisualStudio.Component.CoreEditor" Version="[17.0,18.0)" DisplayName="Visual Studio core editor" />
    </Prerequisites>
    <Assets>
        <Asset Type="Microsoft.VisualStudio.VsPackage" d:Source="Project" d:ProjectName="%CurrentProject%" Path="|%CurrentProject%;PkgdefProjectOutputGroup|" />
        <Asset Type="Microsoft.VisualStudio.ToolboxControl" d:Source="Project" d:ProjectName="%CurrentProject%" Path="|%CurrentProject%|" />
    </Assets>
</PackageManifest>
