# CafeLabs AutoCommit - 安装和使用指南

## 快速开始

### 1. 安装插件

#### 方法一：从源码安装（推荐）
```bash
# 1. 克隆或下载项目
cd Cafelabs-AutoCommit

# 2. 安装依赖
npm install

# 3. 编译项目
npm run compile

# 4. 在VSCode中按F5启动扩展开发主机
# 或者打包为VSIX文件
npm install -g vsce
vsce package
```

#### 方法二：直接安装VSIX包
1. 下载 `cafelabs-autocommit-1.0.0.vsix` 文件
2. 在VSCode中：`Ctrl+Shift+P` → `Extensions: Install from VSIX...`
3. 选择下载的VSIX文件

### 2. 使用插件

1. **打开Git项目**：确保你的工作区是一个Git仓库
2. **找到AutoCommit面板**：在左侧活动栏中点击Git提交图标
3. **配置设置**：
   - **定时提交**：设置每日固定时间自动提交
   - **间隔提交**：设置循环间隔自动提交
4. **启用AutoCommit**：点击"Enable AutoCommit"按钮

## 功能详解

### 🕐 定时提交 (Scheduled Commit)
- 每天在指定时间自动提交
- 可设置时:分:秒
- 适合每日工作结束时的自动备份

### 🔄 间隔提交 (Interval Commit)
- 按设定间隔循环提交
- 可设置小时:分钟:秒
- 适合频繁变更的项目

### ⚡ 手动控制
- 随时启用/禁用AutoCommit
- 手动触发提交
- 实时状态显示

## 测试插件

### 运行测试脚本
```bash
# 在插件目录下运行
node test-script.js
```

### 手动测试步骤
1. 打开 `test-project` 文件夹
2. 修改 `test.js` 或 `README.md` 文件
3. 在AutoCommit面板中：
   - 设置间隔提交为30秒
   - 启用AutoCommit
   - 观察自动提交

## 配置选项

插件会在工作区的 `.vscode/settings.json` 中保存以下配置：

```json
{
  "autocommit.scheduledEnabled": false,
  "autocommit.scheduledHour": 18,
  "autocommit.scheduledMinute": 0,
  "autocommit.scheduledSecond": 0,
  "autocommit.intervalEnabled": true,
  "autocommit.intervalHours": 0,
  "autocommit.intervalMinutes": 30,
  "autocommit.intervalSeconds": 0
}
```

## 命令面板

可通过 `Ctrl+Shift+P` 使用以下命令：
- `AutoCommit: Enable` - 启用自动提交
- `AutoCommit: Disable` - 禁用自动提交
- `AutoCommit: Manual Commit` - 手动提交

## 注意事项

1. **Git仓库检查**：插件只在Git仓库中工作
2. **变更检测**：只有当存在未提交的变更时才会执行提交
3. **提交消息**：自动生成带时间戳的提交消息
4. **性能影响**：插件运行时对性能影响极小

## 故障排除

### 插件不工作
- 确保工作区是Git仓库（运行 `git status` 检查）
- 检查VSCode输出面板中的错误信息
- 重启VSCode

### 自动提交不触发
- 确保有未提交的变更
- 检查定时器设置是否正确
- 查看VSCode开发者工具控制台

### 权限问题
- 确保对Git仓库有写权限
- 检查Git配置（用户名和邮箱）

## 开发和贡献

### 开发环境设置
```bash
git clone <repository>
cd Cafelabs-AutoCommit
npm install
code .
# 按F5启动调试
```

### 项目结构
```
Cafelabs-AutoCommit/
├── src/
│   ├── extension.ts          # 主扩展文件
│   ├── autoCommitProvider.ts # 侧边栏视图
│   ├── timerManager.ts       # 定时器管理
│   └── gitManager.ts         # Git操作
├── test-project/             # 测试项目
├── package.json              # 扩展配置
└── README.md                 # 项目说明
```

## 许可证

MIT License - 详见 LICENSE 文件
