# AutoCommit 故障排除指南

## 🔍 问题诊断步骤

### 1. 检查插件是否已安装
```bash
# 在VSCode中按 Ctrl+Shift+X 打开扩展面板
# 搜索 "CafeLabs AutoCommit" 或 "cafelabs-autocommit"
# 确认插件显示为"已启用"状态
```

### 2. 检查插件是否激活
```bash
# 方法1: 查看VSCode输出
# Ctrl+Shift+U 打开输出面板
# 在下拉菜单中选择 "CafeLabs AutoCommit"

# 方法2: 查看开发者控制台
# Ctrl+Shift+I 打开开发者工具
# 查看Console标签页中的日志
```

### 3. 查找侧边栏图标
- 在左侧活动栏中寻找Git提交图标 ($(git-commit))
- 如果没有看到，尝试右键点击活动栏，选择"重置活动栏"
- 或者在命令面板中搜索"AutoCommit"相关命令

### 4. 手动激活插件
```bash
# 按 Ctrl+Shift+P 打开命令面板
# 输入: "AutoCommit: Test Command"
# 如果命令存在，说明插件已加载
```

## 🛠️ 常见问题解决方案

### 问题1: 插件安装后没有任何显示

**可能原因:**
- 插件未正确激活
- VSCode版本不兼容
- 插件文件损坏

**解决方案:**
```bash
# 1. 重新安装插件
# 卸载现有插件，重启VSCode，重新安装

# 2. 检查VSCode版本
# 确保VSCode版本 >= 1.74.0

# 3. 使用调试版本
# 安装 cafelabs-autocommit-debug-1.0.2.vsix
```

### 问题2: 看不到侧边栏图标

**解决方案:**
```bash
# 1. 重置活动栏
# 右键点击左侧活动栏 → "重置活动栏"

# 2. 手动显示视图
# Ctrl+Shift+P → "View: Show AutoCommit"

# 3. 检查视图容器
# 查看 View → Open View → AutoCommit
```

### 问题3: 侧边栏显示空白

**可能原因:**
- Webview加载失败
- JavaScript被禁用
- 样式文件问题

**解决方案:**
```bash
# 1. 启用脚本
# 确保VSCode设置中允许扩展运行脚本

# 2. 重新加载窗口
# Ctrl+Shift+P → "Developer: Reload Window"

# 3. 查看控制台错误
# Ctrl+Shift+I → Console 查看错误信息
```

## 🔧 调试步骤

### 步骤1: 安装调试版本
```bash
# 1. 编译调试版本
cp package-debug.json package.json
npm run compile
vsce package --allow-star-activation

# 2. 安装调试版本
# 在VSCode中安装生成的 .vsix 文件
```

### 步骤2: 查看调试信息
```bash
# 1. 打开开发者工具
# Ctrl+Shift+I

# 2. 查看Console输出
# 应该看到以 "DEBUG:" 开头的日志信息

# 3. 查看网络请求
# Network标签页中查看是否有失败的请求
```

### 步骤3: 测试基本功能
```bash
# 1. 查找AutoCommit图标
# 左侧活动栏应该有Git提交图标

# 2. 点击图标
# 应该显示"Debug Settings"面板

# 3. 测试按钮
# 点击"Test Button"应该显示反馈
```

## 📋 收集诊断信息

如果问题仍然存在，请收集以下信息：

### 系统信息
```bash
# VSCode版本
# Help → About

# 操作系统版本
# Windows: winver
# macOS: About This Mac
# Linux: lsb_release -a
```

### 插件信息
```bash
# 1. 插件列表
# Ctrl+Shift+X → 查看已安装的扩展

# 2. 控制台日志
# Ctrl+Shift+I → Console → 复制所有相关日志

# 3. 输出面板日志
# Ctrl+Shift+U → 选择相关输出通道
```

### 错误信息
```bash
# 1. JavaScript错误
# 开发者工具 → Console → 红色错误信息

# 2. 扩展主机错误
# 输出面板 → "Extension Host" 通道

# 3. VSCode日志
# Help → Toggle Developer Tools → Console
```

## 🚀 快速修复尝试

### 方法1: 重置VSCode
```bash
# 1. 关闭VSCode
# 2. 删除扩展缓存 (可选)
# 3. 重新启动VSCode
# 4. 重新安装插件
```

### 方法2: 使用开发模式
```bash
# 1. 克隆源代码
git clone <repository>
cd Cafelabs-AutoCommit

# 2. 安装依赖
npm install

# 3. 编译
npm run compile

# 4. 按F5启动调试
# 这将打开新的VSCode窗口用于测试
```

### 方法3: 手动注册命令
```bash
# 在命令面板中尝试:
# - "AutoCommit: Enable"
# - "AutoCommit: Disable"  
# - "AutoCommit: Manual Commit"
# - "AutoCommit: Test Command" (调试版本)
```

## 📞 获取帮助

如果以上方法都无法解决问题，请提供：
1. VSCode版本信息
2. 操作系统信息
3. 控制台错误日志
4. 插件安装方式
5. 具体的错误现象描述

这将帮助快速定位和解决问题。
