using System;
using System.IO;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Threading;
using Microsoft.VisualStudio.Shell;
using CafelabsAutoCommit.Models;
using CafelabsAutoCommit.Services;

namespace CafelabsAutoCommit.UI
{
    /// <summary>
    /// AutoCommit工具窗口控件
    /// </summary>
    public partial class AutoCommitToolWindowControl : UserControl
    {
        private readonly SettingsService _settingsService;
        private readonly GitService _gitService;
        private readonly DispatcherTimer _clockTimer;
        private readonly DispatcherTimer _gitStatusTimer;

        public AutoCommitToolWindowControl()
        {
            InitializeComponent();
            
            _settingsService = new SettingsService();
            _gitService = new GitService();
            
            // 初始化时钟定时器
            _clockTimer = new DispatcherTimer
            {
                Interval = TimeSpan.FromSeconds(1)
            };
            _clockTimer.Tick += OnClockTick;
            _clockTimer.Start();
            
            // 初始化Git状态定时器
            _gitStatusTimer = new DispatcherTimer
            {
                Interval = TimeSpan.FromSeconds(5)
            };
            _gitStatusTimer.Tick += OnGitStatusTick;
            _gitStatusTimer.Start();
            
            // 加载设置
            LoadSettings();
            
            // 更新Git状态
            UpdateGitStatus();
        }

        #region 时钟和状态更新

        private void OnClockTick(object sender, EventArgs e)
        {
            TimeText.Text = $"当前时间: {DateTime.Now:HH:mm:ss}";
        }

        private void OnGitStatusTick(object sender, EventArgs e)
        {
            UpdateGitStatus();
        }

        private void UpdateGitStatus()
        {
            try
            {
                var solutionDir = GetSolutionDirectory();
                if (string.IsNullOrEmpty(solutionDir))
                {
                    GitStatusText.Text = "❌ 未找到解决方案目录";
                    return;
                }

                var status = _gitService.GetRepositoryStatus(solutionDir);
                if (!status.IsValid)
                {
                    GitStatusText.Text = $"❌ {status.Message}";
                    return;
                }

                var statusText = $"✅ 当前分支: {status.CurrentBranch}\n";
                if (status.HasChanges)
                {
                    statusText += $"📝 有变更: +{status.AddedFiles} ~{status.ModifiedFiles} -{status.RemovedFiles} ?{status.UntrackedFiles}";
                }
                else
                {
                    statusText += "✨ 工作区干净，无变更";
                }

                GitStatusText.Text = statusText;
            }
            catch (Exception ex)
            {
                GitStatusText.Text = $"❌ 获取Git状态失败: {ex.Message}";
            }
        }

        #endregion

        #region 设置管理

        private void LoadSettings()
        {
            try
            {
                var settings = _settingsService.LoadSettings();
                
                ScheduledEnabledCheckBox.IsChecked = settings.ScheduledEnabled;
                ScheduledHourTextBox.Text = settings.ScheduledHour.ToString();
                ScheduledMinuteTextBox.Text = settings.ScheduledMinute.ToString();
                ScheduledSecondTextBox.Text = settings.ScheduledSecond.ToString();
                
                IntervalEnabledCheckBox.IsChecked = settings.IntervalEnabled;
                IntervalHoursTextBox.Text = settings.IntervalHours.ToString();
                IntervalMinutesTextBox.Text = settings.IntervalMinutes.ToString();
                IntervalSecondsTextBox.Text = settings.IntervalSeconds.ToString();
                
                StatusText.Text = "✅ 设置已加载";
            }
            catch (Exception ex)
            {
                StatusText.Text = $"❌ 加载设置失败: {ex.Message}";
            }
        }

        private AutoCommitSettings GetCurrentSettings()
        {
            return new AutoCommitSettings
            {
                ScheduledEnabled = ScheduledEnabledCheckBox.IsChecked ?? false,
                ScheduledHour = ParseInt(ScheduledHourTextBox.Text, 18),
                ScheduledMinute = ParseInt(ScheduledMinuteTextBox.Text, 0),
                ScheduledSecond = ParseInt(ScheduledSecondTextBox.Text, 0),
                
                IntervalEnabled = IntervalEnabledCheckBox.IsChecked ?? false,
                IntervalHours = ParseInt(IntervalHoursTextBox.Text, 0),
                IntervalMinutes = ParseInt(IntervalMinutesTextBox.Text, 30),
                IntervalSeconds = ParseInt(IntervalSecondsTextBox.Text, 0)
            };
        }

        private int ParseInt(string text, int defaultValue)
        {
            return int.TryParse(text, out int result) ? result : defaultValue;
        }

        #endregion

        #region 事件处理

        private void OnScheduledEnabledChanged(object sender, RoutedEventArgs e)
        {
            var enabled = ScheduledEnabledCheckBox.IsChecked ?? false;
            StatusText.Text = enabled ? "✅ 定时提交已启用" : "⏹️ 定时提交已禁用";
        }

        private void OnIntervalEnabledChanged(object sender, RoutedEventArgs e)
        {
            var enabled = IntervalEnabledCheckBox.IsChecked ?? false;
            StatusText.Text = enabled ? "✅ 间隔提交已启用" : "⏹️ 间隔提交已禁用";
        }

        private void OnTimeSettingChanged(object sender, TextChangedEventArgs e)
        {
            StatusText.Text = "⚠️ 设置已修改，请保存设置";
        }

        private void OnSaveSettingsClick(object sender, RoutedEventArgs e)
        {
            try
            {
                var settings = GetCurrentSettings();
                if (!settings.IsValid())
                {
                    StatusText.Text = "❌ 设置值无效，请检查时间格式";
                    return;
                }
                
                _settingsService.SaveSettings(settings);
                StatusText.Text = "✅ 设置已保存";
            }
            catch (Exception ex)
            {
                StatusText.Text = $"❌ 保存设置失败: {ex.Message}";
            }
        }

        private void OnLoadSettingsClick(object sender, RoutedEventArgs e)
        {
            LoadSettings();
        }

        private void OnEnableAutoCommitClick(object sender, RoutedEventArgs e)
        {
            var settings = GetCurrentSettings();
            StatusText.Text = $"✅ AutoCommit已启用\n{settings.GetDescription()}";
        }

        private void OnDisableAutoCommitClick(object sender, RoutedEventArgs e)
        {
            StatusText.Text = "⏹️ AutoCommit已禁用";
        }

        private void OnManualCommitClick(object sender, RoutedEventArgs e)
        {
            PerformManualCommit();
        }

        private void OnOpenSettingsClick(object sender, RoutedEventArgs e)
        {
            try
            {
                var settingsDialog = new SettingsDialog();
                settingsDialog.ShowDialog();
                
                // 重新加载设置
                LoadSettings();
                StatusText.Text = "✅ 设置面板已关闭，设置已刷新";
            }
            catch (Exception ex)
            {
                StatusText.Text = $"❌ 打开设置面板失败: {ex.Message}";
            }
        }

        private void OnTestClick(object sender, RoutedEventArgs e)
        {
            StatusText.Text = $"🧪 测试按钮点击 - {DateTime.Now:HH:mm:ss}";
        }

        #endregion

        #region Git操作

        private void PerformManualCommit()
        {
            try
            {
                StatusText.Text = "🔄 执行双分支提交中...";
                
                var solutionDir = GetSolutionDirectory();
                if (string.IsNullOrEmpty(solutionDir))
                {
                    StatusText.Text = "❌ 未找到解决方案目录";
                    return;
                }

                var result = _gitService.PerformDualBranchCommit(solutionDir);
                StatusText.Text = result.Success ? result.Message : $"❌ {result.Message}";
                
                // 更新Git状态
                UpdateGitStatus();
            }
            catch (Exception ex)
            {
                StatusText.Text = $"❌ 提交失败: {ex.Message}";
            }
        }

        private string GetSolutionDirectory()
        {
            try
            {
                ThreadHelper.ThrowIfNotOnUIThread();
                
                var dte = Package.GetGlobalService(typeof(EnvDTE.DTE)) as EnvDTE.DTE;
                if (dte?.Solution?.FullName != null)
                {
                    return Path.GetDirectoryName(dte.Solution.FullName);
                }
                
                return null;
            }
            catch
            {
                return null;
            }
        }

        #endregion
    }
}
