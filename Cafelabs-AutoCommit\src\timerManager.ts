import * as vscode from 'vscode';
import { GitManager } from './gitManager';

export interface TimerSettings {
    scheduledEnabled: boolean;
    scheduledHour: number;
    scheduledMinute: number;
    scheduledSecond: number;
    intervalEnabled: boolean;
    intervalHours: number;
    intervalMinutes: number;
    intervalSeconds: number;
}

export class TimerManager {
    private gitManager: GitManager;
    private settings: TimerSettings;
    private scheduledTimer: NodeJS.Timeout | null = null;
    private intervalTimer: NodeJS.Timeout | null = null;
    private isEnabled: boolean = false;
    private onSettingsChanged: vscode.EventEmitter<TimerSettings> = new vscode.EventEmitter<TimerSettings>();
    public readonly onDidChangeSettings: vscode.Event<TimerSettings> = this.onSettingsChanged.event;

    constructor(gitManager: GitManager) {
        this.gitManager = gitManager;
        this.settings = {
            scheduledEnabled: false,
            scheduledHour: 9,
            scheduledMinute: 0,
            scheduledSecond: 0,
            intervalEnabled: false,
            intervalHours: 1,
            intervalMinutes: 0,
            intervalSeconds: 0
        };
        this.loadSettings();
    }

    private loadSettings() {
        const config = vscode.workspace.getConfiguration('autocommit');
        this.settings = {
            scheduledEnabled: config.get('scheduledEnabled', false),
            scheduledHour: config.get('scheduledHour', 9),
            scheduledMinute: config.get('scheduledMinute', 0),
            scheduledSecond: config.get('scheduledSecond', 0),
            intervalEnabled: config.get('intervalEnabled', false),
            intervalHours: config.get('intervalHours', 1),
            intervalMinutes: config.get('intervalMinutes', 0),
            intervalSeconds: config.get('intervalSeconds', 0)
        };
    }

    private async saveSettings() {
        const config = vscode.workspace.getConfiguration('autocommit');
        await config.update('scheduledEnabled', this.settings.scheduledEnabled, vscode.ConfigurationTarget.Workspace);
        await config.update('scheduledHour', this.settings.scheduledHour, vscode.ConfigurationTarget.Workspace);
        await config.update('scheduledMinute', this.settings.scheduledMinute, vscode.ConfigurationTarget.Workspace);
        await config.update('scheduledSecond', this.settings.scheduledSecond, vscode.ConfigurationTarget.Workspace);
        await config.update('intervalEnabled', this.settings.intervalEnabled, vscode.ConfigurationTarget.Workspace);
        await config.update('intervalHours', this.settings.intervalHours, vscode.ConfigurationTarget.Workspace);
        await config.update('intervalMinutes', this.settings.intervalMinutes, vscode.ConfigurationTarget.Workspace);
        await config.update('intervalSeconds', this.settings.intervalSeconds, vscode.ConfigurationTarget.Workspace);
        
        this.onSettingsChanged.fire(this.settings);
    }

    updateSettings(newSettings: Partial<TimerSettings>) {
        this.settings = { ...this.settings, ...newSettings };
        this.saveSettings();
        
        if (this.isEnabled) {
            this.restartTimers();
        }
    }

    getSettings(): TimerSettings {
        return { ...this.settings };
    }

    enableAutoCommit() {
        this.isEnabled = true;
        this.startTimers();
    }

    disableAutoCommit() {
        this.isEnabled = false;
        this.stopTimers();
    }

    private startTimers() {
        this.stopTimers();

        if (this.settings.scheduledEnabled) {
            this.startScheduledTimer();
        }

        if (this.settings.intervalEnabled) {
            this.startIntervalTimer();
        }
    }

    private stopTimers() {
        if (this.scheduledTimer) {
            clearTimeout(this.scheduledTimer);
            this.scheduledTimer = null;
        }

        if (this.intervalTimer) {
            clearInterval(this.intervalTimer);
            this.intervalTimer = null;
        }
    }

    private restartTimers() {
        if (this.isEnabled) {
            this.startTimers();
        }
    }

    private startScheduledTimer() {
        const now = new Date();
        const scheduled = new Date();
        scheduled.setHours(this.settings.scheduledHour, this.settings.scheduledMinute, this.settings.scheduledSecond, 0);

        // If the scheduled time has passed today, schedule for tomorrow
        if (scheduled <= now) {
            scheduled.setDate(scheduled.getDate() + 1);
        }

        const timeUntilScheduled = scheduled.getTime() - now.getTime();

        this.scheduledTimer = setTimeout(async () => {
            await this.performCommit('Scheduled commit');
            // Reschedule for next day
            this.startScheduledTimer();
        }, timeUntilScheduled);

        console.log(`Scheduled commit set for: ${scheduled.toLocaleString()}`);
    }

    private startIntervalTimer() {
        const intervalMs = (this.settings.intervalHours * 3600 + 
                          this.settings.intervalMinutes * 60 + 
                          this.settings.intervalSeconds) * 1000;

        if (intervalMs > 0) {
            this.intervalTimer = setInterval(async () => {
                await this.performCommit('Interval commit');
            }, intervalMs);

            console.log(`Interval commit set for every ${intervalMs / 1000} seconds`);
        }
    }

    private async performCommit(type: string) {
        try {
            const isGitRepo = await this.gitManager.checkGitRepository();
            if (!isGitRepo) {
                console.log('Not a git repository, skipping commit');
                return;
            }

            const result = await this.gitManager.commitChanges();
            if (result.success) {
                vscode.window.showInformationMessage(`${type}: ${result.message}`);
            } else {
                console.log(`${type} skipped: ${result.message}`);
            }
        } catch (error) {
            console.error(`Error during ${type}:`, error);
        }
    }

    dispose() {
        this.stopTimers();
        this.onSettingsChanged.dispose();
    }
}
