<UserControl x:Class="CafelabsAutoCommit.UI.AutoCommitToolWindowControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             mc:Ignorable="d"
             d:DesignHeight="600" d:DesignWidth="350"
             Background="{DynamicResource {x:Static SystemColors.WindowBrushKey}}">
    
    <UserControl.Resources>
        <Style x:Key="HeaderStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="#4CAF50"/>
            <Setter Property="Margin" Value="0,10,0,5"/>
        </Style>
        
        <Style x:Key="SectionStyle" TargetType="Border">
            <Setter Property="BorderBrush" Value="#4CAF50"/>
            <Setter Property="BorderThickness" Value="2"/>
            <Setter Property="CornerRadius" Value="5"/>
            <Setter Property="Padding" Value="10"/>
            <Setter Property="Margin" Value="0,5"/>
            <Setter Property="Background" Value="#F8F9FA"/>
        </Style>
        
        <Style x:Key="ButtonStyle" TargetType="Button">
            <Setter Property="Padding" Value="10,5"/>
            <Setter Property="Margin" Value="2"/>
            <Setter Property="FontWeight" Value="Bold"/>
        </Style>
        
        <Style x:Key="ManualCommitButtonStyle" TargetType="Button" BasedOn="{StaticResource ButtonStyle}">
            <Setter Property="Background" Value="#FF6B35"/>
            <Setter Property="Foreground" Value="White"/>
        </Style>
    </UserControl.Resources>

    <ScrollViewer VerticalScrollBarVisibility="Auto">
        <StackPanel Margin="10">
            <!-- 标题 -->
            <TextBlock Text="🚀 CafeLabs AutoCommit" 
                       FontSize="20" 
                       FontWeight="Bold" 
                       HorizontalAlignment="Center" 
                       Foreground="#4CAF50" 
                       Margin="0,0,0,20"/>

            <!-- 状态显示 -->
            <Border Style="{StaticResource SectionStyle}">
                <StackPanel>
                    <TextBlock Text="📊 状态信息" Style="{StaticResource HeaderStyle}"/>
                    <TextBlock x:Name="StatusText" 
                               Text="✅ AutoCommit 工具窗口已加载" 
                               TextWrapping="Wrap"
                               Margin="0,5"/>
                    <TextBlock x:Name="TimeText" 
                               Text="当前时间: --:--:--" 
                               FontSize="12"
                               Foreground="Gray"
                               Margin="0,2"/>
                </StackPanel>
            </Border>

            <!-- 定时提交设置 -->
            <Border Style="{StaticResource SectionStyle}">
                <StackPanel>
                    <TextBlock Text="🕐 定时提交" Style="{StaticResource HeaderStyle}"/>
                    <CheckBox x:Name="ScheduledEnabledCheckBox" 
                              Content="启用定时提交 (每日指定时间)" 
                              Margin="0,5"
                              Checked="OnScheduledEnabledChanged"
                              Unchecked="OnScheduledEnabledChanged"/>
                    
                    <StackPanel Orientation="Horizontal" Margin="0,5">
                        <TextBlock Text="时间:" VerticalAlignment="Center" Margin="0,0,10,0"/>
                        <TextBox x:Name="ScheduledHourTextBox" 
                                 Width="40" 
                                 Text="18" 
                                 TextAlignment="Center"
                                 TextChanged="OnTimeSettingChanged"/>
                        <TextBlock Text=":" VerticalAlignment="Center" Margin="5,0"/>
                        <TextBox x:Name="ScheduledMinuteTextBox" 
                                 Width="40" 
                                 Text="0" 
                                 TextAlignment="Center"
                                 TextChanged="OnTimeSettingChanged"/>
                        <TextBlock Text=":" VerticalAlignment="Center" Margin="5,0"/>
                        <TextBox x:Name="ScheduledSecondTextBox" 
                                 Width="40" 
                                 Text="0" 
                                 TextAlignment="Center"
                                 TextChanged="OnTimeSettingChanged"/>
                        <TextBlock Text="(24小时制)" 
                                   VerticalAlignment="Center" 
                                   FontSize="10" 
                                   Foreground="Gray" 
                                   Margin="10,0,0,0"/>
                    </StackPanel>
                </StackPanel>
            </Border>

            <!-- 间隔提交设置 -->
            <Border Style="{StaticResource SectionStyle}">
                <StackPanel>
                    <TextBlock Text="🔄 间隔提交" Style="{StaticResource HeaderStyle}"/>
                    <CheckBox x:Name="IntervalEnabledCheckBox" 
                              Content="启用间隔提交 (重复执行)" 
                              Margin="0,5"
                              Checked="OnIntervalEnabledChanged"
                              Unchecked="OnIntervalEnabledChanged"/>
                    
                    <StackPanel Orientation="Horizontal" Margin="0,5">
                        <TextBlock Text="间隔:" VerticalAlignment="Center" Margin="0,0,10,0"/>
                        <TextBox x:Name="IntervalHoursTextBox" 
                                 Width="40" 
                                 Text="0" 
                                 TextAlignment="Center"
                                 TextChanged="OnTimeSettingChanged"/>
                        <TextBlock Text="小时" VerticalAlignment="Center" Margin="5,0"/>
                        <TextBox x:Name="IntervalMinutesTextBox" 
                                 Width="40" 
                                 Text="30" 
                                 TextAlignment="Center"
                                 TextChanged="OnTimeSettingChanged"/>
                        <TextBlock Text="分钟" VerticalAlignment="Center" Margin="5,0"/>
                        <TextBox x:Name="IntervalSecondsTextBox" 
                                 Width="40" 
                                 Text="0" 
                                 TextAlignment="Center"
                                 TextChanged="OnTimeSettingChanged"/>
                        <TextBlock Text="秒" VerticalAlignment="Center" Margin="5,0"/>
                    </StackPanel>
                </StackPanel>
            </Border>

            <!-- 双分支提交信息 -->
            <Border Style="{StaticResource SectionStyle}" Background="#FFF3E0">
                <StackPanel>
                    <TextBlock Text="📋 双分支提交信息" Style="{StaticResource HeaderStyle}" Foreground="#FF6B35"/>
                    <TextBlock Text="• 手工提交将保存到当前分支" FontSize="12" Margin="0,2"/>
                    <TextBlock Text="• 同时自动提交到 cafelabs-autoCommit 分支" FontSize="12" Margin="0,2"/>
                    <TextBlock Text="• 如果分支不存在会自动创建" FontSize="12" Margin="0,2"/>
                    <TextBlock Text="• 安全的分支切换和错误恢复" FontSize="12" Margin="0,2"/>
                </StackPanel>
            </Border>

            <!-- 控制按钮 -->
            <Border Style="{StaticResource SectionStyle}">
                <StackPanel>
                    <TextBlock Text="⚡ 控制面板" Style="{StaticResource HeaderStyle}"/>
                    
                    <UniformGrid Columns="2" Margin="0,5">
                        <Button x:Name="SaveSettingsButton" 
                                Content="💾 保存设置" 
                                Style="{StaticResource ButtonStyle}"
                                Background="#4CAF50"
                                Foreground="White"
                                Click="OnSaveSettingsClick"/>
                        <Button x:Name="LoadSettingsButton" 
                                Content="📥 加载设置" 
                                Style="{StaticResource ButtonStyle}"
                                Click="OnLoadSettingsClick"/>
                    </UniformGrid>
                    
                    <UniformGrid Columns="2" Margin="0,5">
                        <Button x:Name="EnableAutoCommitButton" 
                                Content="▶️ 启用自动提交" 
                                Style="{StaticResource ButtonStyle}"
                                Click="OnEnableAutoCommitClick"/>
                        <Button x:Name="DisableAutoCommitButton" 
                                Content="⏹️ 禁用自动提交" 
                                Style="{StaticResource ButtonStyle}"
                                Click="OnDisableAutoCommitClick"/>
                    </UniformGrid>
                    
                    <Button x:Name="ManualCommitButton" 
                            Content="🚀 手工提交 (双分支)" 
                            Style="{StaticResource ManualCommitButtonStyle}"
                            Click="OnManualCommitClick"
                            Margin="0,10,0,5"/>
                    
                    <Button x:Name="OpenSettingsButton" 
                            Content="🔧 打开设置面板" 
                            Style="{StaticResource ButtonStyle}"
                            Click="OnOpenSettingsClick"
                            Margin="0,5"/>
                    
                    <Button x:Name="TestButton" 
                            Content="🧪 测试功能" 
                            Style="{StaticResource ButtonStyle}"
                            Click="OnTestClick"
                            Margin="0,5"/>
                </StackPanel>
            </Border>

            <!-- Git状态信息 -->
            <Border Style="{StaticResource SectionStyle}">
                <StackPanel>
                    <TextBlock Text="📁 Git 状态" Style="{StaticResource HeaderStyle}"/>
                    <TextBlock x:Name="GitStatusText" 
                               Text="检查Git状态中..." 
                               TextWrapping="Wrap"
                               FontSize="12"
                               Margin="0,5"/>
                </StackPanel>
            </Border>
        </StackPanel>
    </ScrollViewer>
</UserControl>
