{"version": 3, "file": "extension.js", "sourceRoot": "", "sources": ["../src/extension.ts"], "names": [], "mappings": ";;;AAAA,iCAAiC;AACjC,6DAA0D;AAC1D,iDAA8C;AAC9C,6CAA0C;AAE1C,IAAI,kBAAsC,CAAC;AAC3C,IAAI,YAA0B,CAAC;AAC/B,IAAI,UAAsB,CAAC;AAE3B,SAAgB,QAAQ,CAAC,OAAgC;IACrD,OAAO,CAAC,GAAG,CAAC,8CAA8C,CAAC,CAAC;IAE5D,sBAAsB;IACtB,UAAU,GAAG,IAAI,uBAAU,EAAE,CAAC;IAC9B,YAAY,GAAG,IAAI,2BAAY,CAAC,UAAU,CAAC,CAAC;IAC5C,kBAAkB,GAAG,IAAI,uCAAkB,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;IAEnE,gCAAgC;IAChC,OAAO,CAAC,aAAa,CAAC,IAAI,CACtB,MAAM,CAAC,MAAM,CAAC,2BAA2B,CAAC,oBAAoB,EAAE,kBAAkB,CAAC,CACtF,CAAC;IAEF,oBAAoB;IACpB,OAAO,CAAC,aAAa,CAAC,IAAI,CACtB,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,mBAAmB,EAAE,GAAG,EAAE;QACtD,YAAY,CAAC,gBAAgB,EAAE,CAAC;QAChC,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,oBAAoB,CAAC,CAAC;IAC/D,CAAC,CAAC,CACL,CAAC;IAEF,OAAO,CAAC,aAAa,CAAC,IAAI,CACtB,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,oBAAoB,EAAE,GAAG,EAAE;QACvD,YAAY,CAAC,iBAAiB,EAAE,CAAC;QACjC,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,qBAAqB,CAAC,CAAC;IAChE,CAAC,CAAC,CACL,CAAC;IAEF,OAAO,CAAC,aAAa,CAAC,IAAI,CACtB,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,yBAAyB,EAAE,KAAK,IAAI,EAAE;QAClE,MAAM,MAAM,GAAG,MAAM,UAAU,CAAC,aAAa,EAAE,CAAC;QAChD,IAAI,MAAM,CAAC,OAAO,EAAE;YAChB,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,6BAA6B,MAAM,CAAC,OAAO,EAAE,CAAC,CAAC;SACvF;aAAM;YACH,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,yBAAyB,MAAM,CAAC,OAAO,EAAE,CAAC,CAAC;SAC7E;IACL,CAAC,CAAC,CACL,CAAC;IAEF,8CAA8C;IAC9C,IAAI,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE;QACnC,UAAU,CAAC,kBAAkB,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;YACzC,IAAI,KAAK,EAAE;gBACP,OAAO,CAAC,GAAG,CAAC,2CAA2C,CAAC,CAAC;aAC5D;QACL,CAAC,CAAC,CAAC;KACN;AACL,CAAC;AA/CD,4BA+CC;AAED,SAAgB,UAAU;IACtB,IAAI,YAAY,EAAE;QACd,YAAY,CAAC,OAAO,EAAE,CAAC;KAC1B;AACL,CAAC;AAJD,gCAIC"}