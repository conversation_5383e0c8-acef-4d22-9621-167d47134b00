using System;
using Microsoft.VisualStudio.Settings;
using Microsoft.VisualStudio.Shell;
using Microsoft.VisualStudio.Shell.Settings;
using CafelabsAutoCommit.Models;

namespace CafelabsAutoCommit.Services
{
    /// <summary>
    /// 设置持久化服务
    /// </summary>
    public class SettingsService
    {
        private const string COLLECTION_PATH = "CafelabsAutoCommit";
        private readonly WritableSettingsStore _settingsStore;

        public SettingsService()
        {
            ThreadHelper.ThrowIfNotOnUIThread();
            
            var settingsManager = new ShellSettingsManager(ServiceProvider.GlobalProvider);
            _settingsStore = settingsManager.GetWritableSettingsStore(SettingsScope.UserSettings);
            
            // 确保集合存在
            if (!_settingsStore.CollectionExists(COLLECTION_PATH))
            {
                _settingsStore.CreateCollection(COLLECTION_PATH);
            }
        }

        /// <summary>
        /// 保存设置
        /// </summary>
        public void SaveSettings(AutoCommitSettings settings)
        {
            try
            {
                ThreadHelper.ThrowIfNotOnUIThread();

                _settingsStore.SetBoolean(COLLECTION_PATH, "ScheduledEnabled", settings.ScheduledEnabled);
                _settingsStore.SetInt32(COLLECTION_PATH, "ScheduledHour", settings.ScheduledHour);
                _settingsStore.SetInt32(COLLECTION_PATH, "ScheduledMinute", settings.ScheduledMinute);
                _settingsStore.SetInt32(COLLECTION_PATH, "ScheduledSecond", settings.ScheduledSecond);
                
                _settingsStore.SetBoolean(COLLECTION_PATH, "IntervalEnabled", settings.IntervalEnabled);
                _settingsStore.SetInt32(COLLECTION_PATH, "IntervalHours", settings.IntervalHours);
                _settingsStore.SetInt32(COLLECTION_PATH, "IntervalMinutes", settings.IntervalMinutes);
                _settingsStore.SetInt32(COLLECTION_PATH, "IntervalSeconds", settings.IntervalSeconds);

                // 保存最后更新时间
                _settingsStore.SetString(COLLECTION_PATH, "LastUpdated", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"保存设置失败: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 加载设置
        /// </summary>
        public AutoCommitSettings LoadSettings()
        {
            try
            {
                ThreadHelper.ThrowIfNotOnUIThread();

                var settings = new AutoCommitSettings();

                if (_settingsStore.PropertyExists(COLLECTION_PATH, "ScheduledEnabled"))
                {
                    settings.ScheduledEnabled = _settingsStore.GetBoolean(COLLECTION_PATH, "ScheduledEnabled", false);
                    settings.ScheduledHour = _settingsStore.GetInt32(COLLECTION_PATH, "ScheduledHour", 18);
                    settings.ScheduledMinute = _settingsStore.GetInt32(COLLECTION_PATH, "ScheduledMinute", 0);
                    settings.ScheduledSecond = _settingsStore.GetInt32(COLLECTION_PATH, "ScheduledSecond", 0);
                    
                    settings.IntervalEnabled = _settingsStore.GetBoolean(COLLECTION_PATH, "IntervalEnabled", false);
                    settings.IntervalHours = _settingsStore.GetInt32(COLLECTION_PATH, "IntervalHours", 0);
                    settings.IntervalMinutes = _settingsStore.GetInt32(COLLECTION_PATH, "IntervalMinutes", 30);
                    settings.IntervalSeconds = _settingsStore.GetInt32(COLLECTION_PATH, "IntervalSeconds", 0);
                }

                return settings;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"加载设置失败: {ex.Message}");
                return new AutoCommitSettings(); // 返回默认设置
            }
        }

        /// <summary>
        /// 重置设置为默认值
        /// </summary>
        public void ResetSettings()
        {
            try
            {
                ThreadHelper.ThrowIfNotOnUIThread();

                if (_settingsStore.CollectionExists(COLLECTION_PATH))
                {
                    _settingsStore.DeleteCollection(COLLECTION_PATH);
                    _settingsStore.CreateCollection(COLLECTION_PATH);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"重置设置失败: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 获取设置的最后更新时间
        /// </summary>
        public DateTime? GetLastUpdatedTime()
        {
            try
            {
                ThreadHelper.ThrowIfNotOnUIThread();

                if (_settingsStore.PropertyExists(COLLECTION_PATH, "LastUpdated"))
                {
                    var timeString = _settingsStore.GetString(COLLECTION_PATH, "LastUpdated", "");
                    if (DateTime.TryParse(timeString, out DateTime result))
                    {
                        return result;
                    }
                }
                return null;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"获取最后更新时间失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 检查设置是否存在
        /// </summary>
        public bool SettingsExist()
        {
            try
            {
                ThreadHelper.ThrowIfNotOnUIThread();
                return _settingsStore.CollectionExists(COLLECTION_PATH) && 
                       _settingsStore.PropertyExists(COLLECTION_PATH, "ScheduledEnabled");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"检查设置存在性失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 导出设置为JSON字符串
        /// </summary>
        public string ExportSettings()
        {
            try
            {
                var settings = LoadSettings();
                return System.Text.Json.JsonSerializer.Serialize(settings, new System.Text.Json.JsonSerializerOptions 
                { 
                    WriteIndented = true 
                });
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"导出设置失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 从JSON字符串导入设置
        /// </summary>
        public bool ImportSettings(string json)
        {
            try
            {
                var settings = System.Text.Json.JsonSerializer.Deserialize<AutoCommitSettings>(json);
                if (settings != null && settings.IsValid())
                {
                    SaveSettings(settings);
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"导入设置失败: {ex.Message}");
                return false;
            }
        }
    }
}
