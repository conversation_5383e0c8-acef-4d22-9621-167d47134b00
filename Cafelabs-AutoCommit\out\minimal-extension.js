"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.deactivate = exports.activate = void 0;
const vscode = require("vscode");
const child_process_1 = require("child_process");
function activate(context) {
    console.log('=== MINIMAL AUTOCOMMIT STARTING ===');
    // 立即显示消息
    vscode.window.showInformationMessage('🚀 Minimal AutoCommit activated!');
    // 注册测试命令
    const testCommand = vscode.commands.registerCommand('autocommit.test', () => {
        vscode.window.showInformationMessage('✅ AutoCommit test command works!');
    });
    context.subscriptions.push(testCommand);
    // 注册手工提交命令
    const manualCommitCommand = vscode.commands.registerCommand('autocommit.manualCommit', async () => {
        await performManualCommit();
    });
    context.subscriptions.push(manualCommitCommand);
    // 注册弹窗设置命令
    const openSettingsCommand = vscode.commands.registerCommand('autocommit.openSettings', () => {
        openSettingsPanel(context);
    });
    context.subscriptions.push(openSettingsCommand);
    // 注册状态栏项目
    const statusBarItem = vscode.window.createStatusBarItem(vscode.StatusBarAlignment.Right, 100);
    statusBarItem.text = "$(git-commit) AutoCommit";
    statusBarItem.tooltip = "AutoCommit is active";
    statusBarItem.command = 'autocommit.test';
    statusBarItem.show();
    context.subscriptions.push(statusBarItem);
    // 尝试注册视图容器
    try {
        console.log('=== REGISTERING VIEW PROVIDER ===');
        const provider = new MinimalViewProvider(context);
        const registration = vscode.window.registerWebviewViewProvider('autocommitSettings', provider, {
            webviewOptions: {
                retainContextWhenHidden: true
            }
        });
        context.subscriptions.push(registration);
        console.log('=== VIEW PROVIDER REGISTERED ===');
        vscode.window.showInformationMessage('📋 AutoCommit view provider registered');
    }
    catch (error) {
        console.error('=== ERROR REGISTERING VIEW ===', error);
        vscode.window.showErrorMessage(`AutoCommit view error: ${error}`);
    }
    // 延迟检查
    setTimeout(() => {
        console.log('=== DELAYED CHECK ===');
        vscode.window.showInformationMessage('🔍 AutoCommit delayed check - extension should be visible now');
    }, 2000);
    console.log('=== MINIMAL AUTOCOMMIT ACTIVATION COMPLETE ===');
}
exports.activate = activate;
class MinimalViewProvider {
    constructor(context) {
        this.context = context;
    }
    resolveWebviewView(webviewView, context, _token) {
        console.log('=== WEBVIEW RESOLVING ===');
        vscode.window.showInformationMessage('🎯 AutoCommit webview is resolving...');
        webviewView.webview.options = {
            enableScripts: true,
            localResourceRoots: [this.context.extensionUri]
        };
        webviewView.webview.html = this.getMinimalHtml();
        // 处理来自webview的消息
        webviewView.webview.onDidReceiveMessage(async (message) => {
            console.log('Received message from webview:', message);
            switch (message.command) {
                case 'manualCommit':
                    console.log('Executing manual commit from webview');
                    await vscode.commands.executeCommand('autocommit.manualCommit');
                    break;
                default:
                    console.log('Unknown command:', message.command);
            }
        }, undefined, this.context.subscriptions);
        console.log('=== WEBVIEW HTML SET ===');
        vscode.window.showInformationMessage('✅ AutoCommit webview HTML loaded');
    }
    getMinimalHtml() {
        return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AutoCommit Minimal</title>
    <style>
        body {
            font-family: var(--vscode-font-family);
            color: var(--vscode-foreground);
            background-color: var(--vscode-editor-background);
            padding: 20px;
            text-align: center;
        }
        .success {
            color: #4CAF50;
            font-size: 18px;
            margin: 20px 0;
        }
        .section {
            margin: 20px 0;
            padding: 15px;
            border: 2px solid #4CAF50;
            border-radius: 8px;
            background-color: var(--vscode-textBlockQuote-background);
        }
        button {
            background-color: var(--vscode-button-background);
            color: var(--vscode-button-foreground);
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 10px;
            font-size: 14px;
        }
        button:hover {
            background-color: var(--vscode-button-hoverBackground);
        }
        input[type="number"] {
            width: 60px;
            padding: 8px;
            border: 1px solid var(--vscode-input-border);
            background-color: var(--vscode-input-background);
            color: var(--vscode-input-foreground);
            margin: 5px;
        }
        .checkbox-container {
            margin: 15px 0;
            text-align: left;
        }
    </style>
</head>
<body>
    <div class="success">🎉 AutoCommit Plugin Working!</div>
    
    <div class="section">
        <h2>🕐 Scheduled Commit</h2>
        <div class="checkbox-container">
            <label>
                <input type="checkbox" id="scheduledEnabled"> Enable scheduled commit
            </label>
        </div>
        <div>
            <label>Time:</label>
            <input type="number" id="scheduledHour" min="0" max="23" value="18" placeholder="HH">:
            <input type="number" id="scheduledMinute" min="0" max="59" value="0" placeholder="MM">:
            <input type="number" id="scheduledSecond" min="0" max="59" value="0" placeholder="SS">
        </div>
    </div>

    <div class="section">
        <h2>🔄 Interval Commit</h2>
        <div class="checkbox-container">
            <label>
                <input type="checkbox" id="intervalEnabled"> Enable interval commit
            </label>
        </div>
        <div>
            <label>Every:</label>
            <input type="number" id="intervalHours" min="0" max="23" value="0">h
            <input type="number" id="intervalMinutes" min="0" max="59" value="30">m
            <input type="number" id="intervalSeconds" min="0" max="59" value="0">s
        </div>
    </div>

    <div class="section">
        <h2>⚡ Controls</h2>
        <button onclick="enableAutoCommit()">Enable AutoCommit</button>
        <button onclick="disableAutoCommit()">Disable AutoCommit</button>
        <button onclick="testFunction()">Test Function</button>
        <button onclick="manualCommit()" style="background-color: #ff6b35; font-weight: bold;">🚀 Manual Commit (Dual Branch)</button>
    </div>

    <div class="section" style="background-color: var(--vscode-textCodeBlock-background); border-color: #ff6b35;">
        <h3>📋 Dual Branch Commit Info</h3>
        <p style="font-size: 12px; color: var(--vscode-descriptionForeground);">
            Manual commit will:<br>
            • Commit to current branch<br>
            • Also commit to <strong>cafelabs-autoCommit</strong> branch<br>
            • Create the branch if it doesn't exist
        </p>
    </div>

    <div id="status" style="margin-top: 20px; padding: 10px; background-color: var(--vscode-textBlockQuote-background); border-radius: 4px;">
        ✅ AutoCommit interface loaded successfully!
    </div>

    <script>
        console.log('AutoCommit webview script loaded');
        
        function testFunction() {
            document.getElementById('status').innerHTML = '🧪 Test button clicked at ' + new Date().toLocaleTimeString();
            console.log('Test function executed');
        }

        function enableAutoCommit() {
            const scheduled = document.getElementById('scheduledEnabled').checked;
            const interval = document.getElementById('intervalEnabled').checked;
            document.getElementById('status').innerHTML = 
                '✅ AutoCommit enabled! Scheduled: ' + scheduled + ', Interval: ' + interval;
        }

        function disableAutoCommit() {
            document.getElementById('status').innerHTML = '⏹️ AutoCommit disabled!';
        }

        function manualCommit() {
            document.getElementById('status').innerHTML = '🔄 Executing dual branch commit...';
            console.log('Manual dual branch commit button clicked');

            // 调用VSCode命令
            if (typeof acquireVsCodeApi !== 'undefined') {
                const vscode = acquireVsCodeApi();
                vscode.postMessage({
                    command: 'manualCommit'
                });

                // 显示进度信息
                setTimeout(() => {
                    document.getElementById('status').innerHTML = '📤 Committing to current branch...';
                }, 500);

                setTimeout(() => {
                    document.getElementById('status').innerHTML = '🔄 Committing to cafelabs-autoCommit branch...';
                }, 2000);

                setTimeout(() => {
                    document.getElementById('status').innerHTML = '✅ Dual branch commit completed! Check notifications for details.';
                }, 4000);

            } else {
                // 备用方案：直接调用命令
                console.log('Fallback: calling command directly');
                setTimeout(() => {
                    document.getElementById('status').innerHTML = '⚠️ Dual branch commit initiated (check notifications)';
                }, 500);
            }
        }

        // 自动显示当前时间
        setInterval(() => {
            const now = new Date();
            document.getElementById('status').innerHTML = 
                '🕐 Current time: ' + now.toLocaleTimeString() + ' - AutoCommit ready!';
        }, 5000);
    </script>
</body>
</html>`;
    }
}
// Git提交功能
async function performManualCommit() {
    try {
        console.log('=== STARTING MANUAL COMMIT ===');
        vscode.window.showInformationMessage('🔄 Starting manual commit...');
        // 获取工作区路径
        const workspaceFolders = vscode.workspace.workspaceFolders;
        if (!workspaceFolders || workspaceFolders.length === 0) {
            vscode.window.showErrorMessage('❌ No workspace folder found');
            return;
        }
        const workspacePath = workspaceFolders[0].uri.fsPath;
        console.log('Workspace path:', workspacePath);
        // 检查是否是Git仓库
        try {
            (0, child_process_1.execSync)('git rev-parse --git-dir', { cwd: workspacePath, stdio: 'pipe' });
        }
        catch (error) {
            vscode.window.showErrorMessage('❌ Not a Git repository');
            return;
        }
        // 获取当前分支名
        let currentBranch;
        try {
            currentBranch = (0, child_process_1.execSync)('git branch --show-current', {
                cwd: workspacePath,
                encoding: 'utf8',
                stdio: 'pipe'
            }).trim();
            console.log('Current branch:', currentBranch);
        }
        catch (error) {
            vscode.window.showErrorMessage('❌ Failed to get current branch');
            return;
        }
        // 检查是否有变更
        let status;
        try {
            status = (0, child_process_1.execSync)('git status --porcelain', {
                cwd: workspacePath,
                encoding: 'utf8',
                stdio: 'pipe'
            });
        }
        catch (error) {
            vscode.window.showErrorMessage('❌ Failed to check Git status');
            return;
        }
        if (!status.trim()) {
            vscode.window.showInformationMessage('ℹ️ No changes to commit');
            return;
        }
        console.log('Git status:', status);
        // 添加所有变更
        try {
            (0, child_process_1.execSync)('git add .', { cwd: workspacePath, stdio: 'pipe' });
            console.log('Files added to staging');
        }
        catch (error) {
            vscode.window.showErrorMessage('❌ Failed to add files to staging');
            return;
        }
        // 生成提交消息
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const commitMessage = `AutoCommit: Manual commit at ${timestamp}`;
        // 1. 提交到当前分支
        try {
            const result = (0, child_process_1.execSync)(`git commit -m "${commitMessage}"`, {
                cwd: workspacePath,
                encoding: 'utf8',
                stdio: 'pipe'
            });
            console.log('Commit to current branch result:', result);
            vscode.window.showInformationMessage(`✅ Committed to current branch (${currentBranch})`);
        }
        catch (error) {
            console.error('Commit to current branch error:', error);
            vscode.window.showErrorMessage('❌ Failed to commit to current branch');
            return;
        }
        // 2. 提交到 cafelabs-autoCommit 分支
        await commitToCafelabsBranch(workspacePath, currentBranch, commitMessage);
    }
    catch (error) {
        console.error('=== MANUAL COMMIT ERROR ===', error);
        vscode.window.showErrorMessage(`❌ Manual commit failed: ${error}`);
    }
}
// 提交到 cafelabs-autoCommit 分支的函数
async function commitToCafelabsBranch(workspacePath, originalBranch, commitMessage) {
    const targetBranch = 'cafelabs-autoCommit';
    try {
        console.log('=== STARTING CAFELABS BRANCH COMMIT ===');
        vscode.window.showInformationMessage('🔄 Committing to cafelabs-autoCommit branch...');
        // 检查 cafelabs-autoCommit 分支是否存在
        let branchExists = false;
        try {
            (0, child_process_1.execSync)(`git show-ref --verify --quiet refs/heads/${targetBranch}`, {
                cwd: workspacePath,
                stdio: 'pipe'
            });
            branchExists = true;
            console.log(`Branch ${targetBranch} exists`);
        }
        catch (error) {
            console.log(`Branch ${targetBranch} does not exist, will create it`);
        }
        if (!branchExists) {
            // 创建新分支
            try {
                (0, child_process_1.execSync)(`git checkout -b ${targetBranch}`, {
                    cwd: workspacePath,
                    stdio: 'pipe'
                });
                console.log(`Created new branch: ${targetBranch}`);
                vscode.window.showInformationMessage(`✅ Created new branch: ${targetBranch}`);
            }
            catch (error) {
                console.error('Failed to create branch:', error);
                vscode.window.showErrorMessage(`❌ Failed to create branch ${targetBranch}`);
                return;
            }
        }
        else {
            // 切换到现有分支
            try {
                (0, child_process_1.execSync)(`git checkout ${targetBranch}`, {
                    cwd: workspacePath,
                    stdio: 'pipe'
                });
                console.log(`Switched to existing branch: ${targetBranch}`);
            }
            catch (error) {
                console.error('Failed to switch to branch:', error);
                vscode.window.showErrorMessage(`❌ Failed to switch to branch ${targetBranch}`);
                return;
            }
        }
        // 合并原分支的最新提交
        try {
            (0, child_process_1.execSync)(`git merge ${originalBranch} --no-edit`, {
                cwd: workspacePath,
                stdio: 'pipe'
            });
            console.log(`Merged ${originalBranch} into ${targetBranch}`);
        }
        catch (error) {
            console.log('Merge conflict or no changes to merge, continuing...');
            // 如果合并失败，可能是因为没有新的提交或冲突，继续执行
        }
        // 切换回原分支
        try {
            (0, child_process_1.execSync)(`git checkout ${originalBranch}`, {
                cwd: workspacePath,
                stdio: 'pipe'
            });
            console.log(`Switched back to original branch: ${originalBranch}`);
            vscode.window.showInformationMessage(`✅ Successfully committed to both ${originalBranch} and ${targetBranch}`);
        }
        catch (error) {
            console.error('Failed to switch back to original branch:', error);
            vscode.window.showWarningMessage(`⚠️ Committed successfully but failed to switch back to ${originalBranch}`);
        }
    }
    catch (error) {
        console.error('=== CAFELABS BRANCH COMMIT ERROR ===', error);
        vscode.window.showErrorMessage(`❌ Failed to commit to ${targetBranch}: ${error}`);
        // 尝试切换回原分支
        try {
            (0, child_process_1.execSync)(`git checkout ${originalBranch}`, {
                cwd: workspacePath,
                stdio: 'pipe'
            });
        }
        catch (switchError) {
            vscode.window.showErrorMessage(`❌ Critical: Failed to switch back to ${originalBranch}`);
        }
    }
}
// 弹出设置面板函数
function openSettingsPanel(context) {
    console.log('=== OPENING SETTINGS PANEL ===');
    // 创建并显示webview面板
    const panel = vscode.window.createWebviewPanel('autocommitSettings', // 标识符
    'AutoCommit Settings', // 面板标题
    vscode.ViewColumn.One, // 显示在编辑器的第一列
    {
        enableScripts: true,
        retainContextWhenHidden: true,
        localResourceRoots: [context.extensionUri] // 本地资源根目录
    });
    // 设置webview内容
    panel.webview.html = getSettingsPanelHtml();
    // 处理来自webview的消息
    panel.webview.onDidReceiveMessage(async (message) => {
        console.log('Settings panel message:', message);
        switch (message.command) {
            case 'manualCommit':
                console.log('Manual commit from settings panel');
                await vscode.commands.executeCommand('autocommit.manualCommit');
                break;
            case 'saveSettings':
                console.log('Saving settings:', message.settings);
                await saveSettings(message.settings);
                vscode.window.showInformationMessage('✅ AutoCommit settings saved!');
                break;
            case 'loadSettings':
                console.log('Loading settings');
                const settings = await loadSettings();
                panel.webview.postMessage({
                    command: 'settingsLoaded',
                    settings: settings
                });
                break;
            default:
                console.log('Unknown settings panel command:', message.command);
        }
    }, undefined, context.subscriptions);
    // 面板关闭时的处理
    panel.onDidDispose(() => {
        console.log('Settings panel disposed');
    }, null, context.subscriptions);
    vscode.window.showInformationMessage('📋 AutoCommit settings panel opened!');
}
// 生成设置面板HTML
function getSettingsPanelHtml() {
    return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AutoCommit Settings</title>
    <style>
        body {
            font-family: var(--vscode-font-family);
            color: var(--vscode-foreground);
            background-color: var(--vscode-editor-background);
            padding: 20px;
            margin: 0;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background-color: var(--vscode-textBlockQuote-background);
            border-radius: 8px;
            border-left: 4px solid #4CAF50;
        }
        .header h1 {
            margin: 0;
            color: #4CAF50;
            font-size: 24px;
        }
        .section {
            margin: 20px 0;
            padding: 20px;
            border: 2px solid #4CAF50;
            border-radius: 8px;
            background-color: var(--vscode-textBlockQuote-background);
        }
        .section h2 {
            margin-top: 0;
            color: #4CAF50;
            font-size: 18px;
        }
        .checkbox-container {
            margin: 15px 0;
            display: flex;
            align-items: center;
        }
        .checkbox-container input[type="checkbox"] {
            margin-right: 10px;
            transform: scale(1.2);
        }
        .time-inputs {
            display: flex;
            align-items: center;
            gap: 10px;
            margin: 15px 0;
            flex-wrap: wrap;
        }
        input[type="number"] {
            width: 70px;
            padding: 8px;
            border: 1px solid var(--vscode-input-border);
            background-color: var(--vscode-input-background);
            color: var(--vscode-input-foreground);
            border-radius: 4px;
            text-align: center;
        }
        .button-container {
            text-align: center;
            margin: 30px 0;
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
        }
        button {
            background-color: var(--vscode-button-background);
            color: var(--vscode-button-foreground);
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
            transition: background-color 0.2s;
        }
        button:hover {
            background-color: var(--vscode-button-hoverBackground);
        }
        .manual-commit-btn {
            background-color: #ff6b35 !important;
            color: white !important;
        }
        .save-btn {
            background-color: #4CAF50 !important;
            color: white !important;
        }
        .status {
            margin-top: 20px;
            padding: 15px;
            background-color: var(--vscode-textCodeBlock-background);
            border-radius: 4px;
            text-align: center;
            font-weight: bold;
        }
        .info-panel {
            background-color: var(--vscode-textCodeBlock-background);
            border: 1px solid #ff6b35;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
        }
        .info-panel h3 {
            margin-top: 0;
            color: #ff6b35;
        }
        .info-panel p {
            font-size: 12px;
            color: var(--vscode-descriptionForeground);
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚀 AutoCommit Settings Panel</h1>
        <p>Configure your automatic Git commit preferences</p>
    </div>

    <div class="section">
        <h2>🕐 Scheduled Commit</h2>
        <div class="checkbox-container">
            <input type="checkbox" id="scheduledEnabled">
            <label for="scheduledEnabled">Enable scheduled commit (daily at specific time)</label>
        </div>
        <div class="time-inputs">
            <label>Time:</label>
            <input type="number" id="scheduledHour" min="0" max="23" value="18" placeholder="HH">
            <span>:</span>
            <input type="number" id="scheduledMinute" min="0" max="59" value="0" placeholder="MM">
            <span>:</span>
            <input type="number" id="scheduledSecond" min="0" max="59" value="0" placeholder="SS">
            <span style="margin-left: 10px; color: var(--vscode-descriptionForeground);">(24-hour format)</span>
        </div>
    </div>

    <div class="section">
        <h2>🔄 Interval Commit</h2>
        <div class="checkbox-container">
            <input type="checkbox" id="intervalEnabled">
            <label for="intervalEnabled">Enable interval commit (repeat every X time)</label>
        </div>
        <div class="time-inputs">
            <label>Every:</label>
            <input type="number" id="intervalHours" min="0" max="23" value="0">
            <span>hours</span>
            <input type="number" id="intervalMinutes" min="0" max="59" value="30">
            <span>minutes</span>
            <input type="number" id="intervalSeconds" min="0" max="59" value="0">
            <span>seconds</span>
        </div>
    </div>

    <div class="info-panel">
        <h3>📋 Dual Branch Commit Info</h3>
        <p>• Manual commit will save to current branch</p>
        <p>• Also automatically commit to <strong>cafelabs-autoCommit</strong> branch</p>
        <p>• Creates the branch if it doesn't exist</p>
        <p>• Safe branch switching with error recovery</p>
    </div>

    <div class="button-container">
        <button onclick="saveSettings()" class="save-btn">💾 Save Settings</button>
        <button onclick="loadSettings()">📥 Load Settings</button>
        <button onclick="enableAutoCommit()">▶️ Enable AutoCommit</button>
        <button onclick="disableAutoCommit()">⏹️ Disable AutoCommit</button>
        <button onclick="manualCommit()" class="manual-commit-btn">🚀 Manual Commit (Dual Branch)</button>
        <button onclick="testFunction()">🧪 Test Function</button>
    </div>

    <div id="status" class="status">
        ✅ AutoCommit settings panel loaded successfully!<br>
        <span style="font-size: 12px;">Current time: <span id="currentTime"></span></span>
    </div>

    <script>
        const vscode = acquireVsCodeApi();
        console.log('AutoCommit settings panel script loaded');

        // 更新当前时间
        function updateCurrentTime() {
            const now = new Date();
            document.getElementById('currentTime').textContent = now.toLocaleTimeString();
        }

        // 每秒更新时间
        setInterval(updateCurrentTime, 1000);
        updateCurrentTime();

        function testFunction() {
            document.getElementById('status').innerHTML = '🧪 Test button clicked at ' + new Date().toLocaleTimeString();
            console.log('Test function executed in settings panel');
        }

        function enableAutoCommit() {
            const scheduled = document.getElementById('scheduledEnabled').checked;
            const interval = document.getElementById('intervalEnabled').checked;
            document.getElementById('status').innerHTML =
                '✅ AutoCommit enabled!<br>Scheduled: ' + scheduled + ', Interval: ' + interval;
        }

        function disableAutoCommit() {
            document.getElementById('status').innerHTML = '⏹️ AutoCommit disabled!';
        }

        function manualCommit() {
            document.getElementById('status').innerHTML = '🔄 Executing dual branch commit...';
            console.log('Manual dual branch commit from settings panel');

            vscode.postMessage({
                command: 'manualCommit'
            });

            // 显示进度
            setTimeout(() => {
                document.getElementById('status').innerHTML = '📤 Committing to current branch...';
            }, 500);

            setTimeout(() => {
                document.getElementById('status').innerHTML = '🔄 Committing to cafelabs-autoCommit branch...';
            }, 2000);

            setTimeout(() => {
                document.getElementById('status').innerHTML = '✅ Dual branch commit completed! Check notifications for details.';
            }, 4000);
        }

        function saveSettings() {
            const settings = {
                scheduledEnabled: document.getElementById('scheduledEnabled').checked,
                scheduledHour: parseInt(document.getElementById('scheduledHour').value),
                scheduledMinute: parseInt(document.getElementById('scheduledMinute').value),
                scheduledSecond: parseInt(document.getElementById('scheduledSecond').value),
                intervalEnabled: document.getElementById('intervalEnabled').checked,
                intervalHours: parseInt(document.getElementById('intervalHours').value),
                intervalMinutes: parseInt(document.getElementById('intervalMinutes').value),
                intervalSeconds: parseInt(document.getElementById('intervalSeconds').value)
            };

            console.log('Saving settings:', settings);
            document.getElementById('status').innerHTML = '💾 Saving settings...';

            vscode.postMessage({
                command: 'saveSettings',
                settings: settings
            });
        }

        function loadSettings() {
            console.log('Loading settings...');
            document.getElementById('status').innerHTML = '📥 Loading settings...';

            vscode.postMessage({
                command: 'loadSettings'
            });
        }

        // 监听来自扩展的消息
        window.addEventListener('message', event => {
            const message = event.data;
            console.log('Received message:', message);

            switch (message.command) {
                case 'settingsLoaded':
                    const settings = message.settings;
                    document.getElementById('scheduledEnabled').checked = settings.scheduledEnabled;
                    document.getElementById('scheduledHour').value = settings.scheduledHour;
                    document.getElementById('scheduledMinute').value = settings.scheduledMinute;
                    document.getElementById('scheduledSecond').value = settings.scheduledSecond;
                    document.getElementById('intervalEnabled').checked = settings.intervalEnabled;
                    document.getElementById('intervalHours').value = settings.intervalHours;
                    document.getElementById('intervalMinutes').value = settings.intervalMinutes;
                    document.getElementById('intervalSeconds').value = settings.intervalSeconds;

                    document.getElementById('status').innerHTML = '✅ Settings loaded successfully!';
                    break;
            }
        });

        // 页面加载完成后自动加载设置
        window.addEventListener('load', () => {
            setTimeout(loadSettings, 500);
        });
    </script>
</body>
</html>`;
}
// 保存设置到VSCode配置
async function saveSettings(settings) {
    try {
        const config = vscode.workspace.getConfiguration('autocommit');
        await config.update('scheduledEnabled', settings.scheduledEnabled, vscode.ConfigurationTarget.Global);
        await config.update('scheduledHour', settings.scheduledHour, vscode.ConfigurationTarget.Global);
        await config.update('scheduledMinute', settings.scheduledMinute, vscode.ConfigurationTarget.Global);
        await config.update('scheduledSecond', settings.scheduledSecond, vscode.ConfigurationTarget.Global);
        await config.update('intervalEnabled', settings.intervalEnabled, vscode.ConfigurationTarget.Global);
        await config.update('intervalHours', settings.intervalHours, vscode.ConfigurationTarget.Global);
        await config.update('intervalMinutes', settings.intervalMinutes, vscode.ConfigurationTarget.Global);
        await config.update('intervalSeconds', settings.intervalSeconds, vscode.ConfigurationTarget.Global);
        console.log('Settings saved successfully:', settings);
    }
    catch (error) {
        console.error('Failed to save settings:', error);
        vscode.window.showErrorMessage('❌ Failed to save settings');
    }
}
// 从VSCode配置加载设置
async function loadSettings() {
    try {
        const config = vscode.workspace.getConfiguration('autocommit');
        const settings = {
            scheduledEnabled: config.get('scheduledEnabled', false),
            scheduledHour: config.get('scheduledHour', 18),
            scheduledMinute: config.get('scheduledMinute', 0),
            scheduledSecond: config.get('scheduledSecond', 0),
            intervalEnabled: config.get('intervalEnabled', false),
            intervalHours: config.get('intervalHours', 0),
            intervalMinutes: config.get('intervalMinutes', 30),
            intervalSeconds: config.get('intervalSeconds', 0)
        };
        console.log('Settings loaded successfully:', settings);
        return settings;
    }
    catch (error) {
        console.error('Failed to load settings:', error);
        vscode.window.showErrorMessage('❌ Failed to load settings');
        // 返回默认设置
        return {
            scheduledEnabled: false,
            scheduledHour: 18,
            scheduledMinute: 0,
            scheduledSecond: 0,
            intervalEnabled: false,
            intervalHours: 0,
            intervalMinutes: 30,
            intervalSeconds: 0
        };
    }
}
function deactivate() {
    console.log('=== MINIMAL AUTOCOMMIT DEACTIVATED ===');
}
exports.deactivate = deactivate;
//# sourceMappingURL=minimal-extension.js.map