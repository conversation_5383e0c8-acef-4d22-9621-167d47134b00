import * as vscode from 'vscode';
import { TimerManager, TimerSettings } from './timerManager';

export class AutoCommitProvider implements vscode.WebviewViewProvider {
    public static readonly viewType = 'autocommitSettings';
    private _view?: vscode.WebviewView;
    private timerManager: TimerManager;

    constructor(
        private readonly _extensionContext: vscode.ExtensionContext,
        timerManager: TimerManager
    ) {
        this.timerManager = timerManager;
        
        // Listen for settings changes
        this.timerManager.onDidChangeSettings(() => {
            this.updateWebview();
        });
    }

    public resolveWebviewView(
        webviewView: vscode.WebviewView,
        context: vscode.WebviewViewResolveContext,
        _token: vscode.CancellationToken,
    ) {
        this._view = webviewView;

        webviewView.webview.options = {
            enableScripts: true,
            localResourceRoots: [
                this._extensionContext.extensionUri
            ]
        };

        console.log('AutoCommit webview resolving...');
        webviewView.webview.html = this._getHtmlForWebview(webviewView.webview);
        console.log('AutoCommit webview HTML set');

        // Handle messages from the webview
        webviewView.webview.onDidReceiveMessage(
            message => {
                switch (message.command) {
                    case 'updateSettings':
                        this.timerManager.updateSettings(message.settings);
                        break;
                    case 'enableAutoCommit':
                        this.timerManager.enableAutoCommit();
                        vscode.window.showInformationMessage('AutoCommit enabled');
                        break;
                    case 'disableAutoCommit':
                        this.timerManager.disableAutoCommit();
                        vscode.window.showInformationMessage('AutoCommit disabled');
                        break;
                }
            },
            undefined,
            this._extensionContext.subscriptions
        );

        this.updateWebview();
    }

    private updateWebview() {
        if (this._view) {
            const settings = this.timerManager.getSettings();
            this._view.webview.postMessage({
                command: 'updateSettings',
                settings: settings
            });
        }
    }

    private _getHtmlForWebview(webview: vscode.Webview) {
        const settings = this.timerManager.getSettings();
        console.log('Generating HTML with settings:', settings);

        return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AutoCommit Settings</title>
    <style>
        body {
            font-family: var(--vscode-font-family);
            font-size: var(--vscode-font-size);
            color: var(--vscode-foreground);
            background-color: var(--vscode-editor-background);
            padding: 10px;
        }
        .section {
            margin-bottom: 20px;
            padding: 10px;
            border: 1px solid var(--vscode-panel-border);
            border-radius: 4px;
        }
        .section h3 {
            margin-top: 0;
            color: var(--vscode-textLink-foreground);
        }
        .form-group {
            margin-bottom: 10px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="number"], input[type="checkbox"] {
            margin-right: 5px;
        }
        input[type="number"] {
            width: 60px;
            padding: 4px;
            border: 1px solid var(--vscode-input-border);
            background-color: var(--vscode-input-background);
            color: var(--vscode-input-foreground);
        }
        .time-inputs {
            display: flex;
            align-items: center;
            gap: 5px;
        }
        .time-inputs span {
            margin: 0 5px;
        }
        button {
            background-color: var(--vscode-button-background);
            color: var(--vscode-button-foreground);
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px 5px 5px 0;
        }
        button:hover {
            background-color: var(--vscode-button-hoverBackground);
        }
        .status {
            margin-top: 10px;
            padding: 8px;
            border-radius: 4px;
            background-color: var(--vscode-textBlockQuote-background);
        }
    </style>
</head>
<body>
    <div class="section">
        <h3>🕐 Scheduled Commit</h3>
        <div class="form-group">
            <label>
                <input type="checkbox" id="scheduledEnabled" ${settings.scheduledEnabled ? 'checked' : ''}>
                Enable scheduled commit
            </label>
        </div>
        <div class="form-group">
            <label>Time:</label>
            <div class="time-inputs">
                <input type="number" id="scheduledHour" min="0" max="23" value="${settings.scheduledHour}">
                <span>:</span>
                <input type="number" id="scheduledMinute" min="0" max="59" value="${settings.scheduledMinute}">
                <span>:</span>
                <input type="number" id="scheduledSecond" min="0" max="59" value="${settings.scheduledSecond}">
            </div>
        </div>
    </div>

    <div class="section">
        <h3>🔄 Interval Commit</h3>
        <div class="form-group">
            <label>
                <input type="checkbox" id="intervalEnabled" ${settings.intervalEnabled ? 'checked' : ''}>
                Enable interval commit
            </label>
        </div>
        <div class="form-group">
            <label>Interval:</label>
            <div class="time-inputs">
                <input type="number" id="intervalHours" min="0" max="23" value="${settings.intervalHours}">
                <span>h</span>
                <input type="number" id="intervalMinutes" min="0" max="59" value="${settings.intervalMinutes}">
                <span>m</span>
                <input type="number" id="intervalSeconds" min="0" max="59" value="${settings.intervalSeconds}">
                <span>s</span>
            </div>
        </div>
    </div>

    <div class="section">
        <h3>⚡ Controls</h3>
        <button onclick="enableAutoCommit()">Enable AutoCommit</button>
        <button onclick="disableAutoCommit()">Disable AutoCommit</button>
        <button onclick="saveSettings()">Save Settings</button>
    </div>

    <div class="status" id="status">
        Ready to configure AutoCommit settings
    </div>

    <script>
        const vscode = acquireVsCodeApi();

        function saveSettings() {
            const settings = {
                scheduledEnabled: document.getElementById('scheduledEnabled').checked,
                scheduledHour: parseInt(document.getElementById('scheduledHour').value),
                scheduledMinute: parseInt(document.getElementById('scheduledMinute').value),
                scheduledSecond: parseInt(document.getElementById('scheduledSecond').value),
                intervalEnabled: document.getElementById('intervalEnabled').checked,
                intervalHours: parseInt(document.getElementById('intervalHours').value),
                intervalMinutes: parseInt(document.getElementById('intervalMinutes').value),
                intervalSeconds: parseInt(document.getElementById('intervalSeconds').value)
            };

            vscode.postMessage({
                command: 'updateSettings',
                settings: settings
            });

            document.getElementById('status').textContent = 'Settings saved!';
        }

        function enableAutoCommit() {
            vscode.postMessage({ command: 'enableAutoCommit' });
            document.getElementById('status').textContent = 'AutoCommit enabled!';
        }

        function disableAutoCommit() {
            vscode.postMessage({ command: 'disableAutoCommit' });
            document.getElementById('status').textContent = 'AutoCommit disabled!';
        }

        // Auto-save on change
        document.addEventListener('change', saveSettings);

        // Listen for messages from the extension
        window.addEventListener('message', event => {
            const message = event.data;
            switch (message.command) {
                case 'updateSettings':
                    const settings = message.settings;
                    document.getElementById('scheduledEnabled').checked = settings.scheduledEnabled;
                    document.getElementById('scheduledHour').value = settings.scheduledHour;
                    document.getElementById('scheduledMinute').value = settings.scheduledMinute;
                    document.getElementById('scheduledSecond').value = settings.scheduledSecond;
                    document.getElementById('intervalEnabled').checked = settings.intervalEnabled;
                    document.getElementById('intervalHours').value = settings.intervalHours;
                    document.getElementById('intervalMinutes').value = settings.intervalMinutes;
                    document.getElementById('intervalSeconds').value = settings.intervalSeconds;
                    break;
            }
        });
    </script>
</body>
</html>`;
    }
}
