{"name": "cafelabs-autocommit-debug", "displayName": "CafeLabs AutoCommit Debug", "description": "Debug version of AutoCommit plugin", "version": "1.0.2", "publisher": "cafelabs", "engines": {"vscode": "^1.74.0"}, "categories": ["Other"], "activationEvents": ["*"], "main": "./out/debug-extension.js", "contributes": {"viewsContainers": {"activitybar": [{"id": "autocommit", "title": "AutoCommit Debug", "icon": "$(git-commit)"}]}, "views": {"autocommit": [{"id": "autocommitSettings", "name": "Debug Settings", "type": "webview"}]}, "commands": [{"command": "autocommit.test", "title": "AutoCommit: Test Command"}]}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "tsc -p ./", "watch": "tsc -watch -p ./"}, "devDependencies": {"@types/vscode": "^1.74.0", "@types/node": "16.x", "typescript": "^4.9.4"}}