{"name": "cafelabs-autocommit-minimal", "displayName": "CafeLabs AutoCommit Minimal", "description": "Minimal test version of AutoCommit plugin", "version": "1.0.6", "publisher": "cafelabs", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/cafelabs/autocommit-vscode.git"}, "engines": {"vscode": "^1.74.0"}, "categories": ["Other"], "activationEvents": ["*"], "main": "./out/minimal-extension.js", "contributes": {"viewsContainers": {"activitybar": [{"id": "autocommit", "title": "AutoCommit Minimal", "icon": "$(git-commit)"}]}, "views": {"autocommit": [{"id": "autocommitSettings", "name": "Minimal Settings", "type": "webview"}]}, "commands": [{"command": "autocommit.test", "title": "AutoCommit: Test Minimal"}, {"command": "autocommit.manualCommit", "title": "AutoCommit: Manual Commit"}]}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "tsc -p ./", "watch": "tsc -watch -p ./"}, "devDependencies": {"@types/vscode": "^1.74.0", "@types/node": "16.x", "typescript": "^4.9.4"}}