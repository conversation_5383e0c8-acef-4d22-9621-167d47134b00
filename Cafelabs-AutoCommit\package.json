{"name": "cafelabs-autocommit", "displayName": "CafeLabs AutoCommit", "description": "Automatic Git commit with customizable timing", "version": "1.0.3", "publisher": "cafelabs", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/cafelabs/autocommit-vscode.git"}, "bugs": {"url": "https://github.com/cafelabs/autocommit-vscode/issues"}, "homepage": "https://github.com/cafelabs/autocommit-vscode#readme", "keywords": ["git", "commit", "auto", "automatic", "timer", "schedule"], "engines": {"vscode": "^1.74.0"}, "categories": ["Other"], "activationEvents": ["onStartupFinished"], "main": "./out/extension.js", "contributes": {"viewsContainers": {"activitybar": [{"id": "autocommit", "title": "AutoCommit", "icon": "$(git-commit)"}]}, "views": {"autocommit": [{"id": "autocommitSettings", "name": "Settings", "type": "webview"}]}, "commands": [{"command": "autocommit.enable", "title": "Enable AutoCommit"}, {"command": "autocommit.disable", "title": "Disable AutoCommit"}, {"command": "autocommit.manualCommit", "title": "Manual Commit"}], "configuration": {"title": "AutoCommit", "properties": {"autocommit.scheduledEnabled": {"type": "boolean", "default": false, "description": "Enable scheduled commits"}, "autocommit.scheduledHour": {"type": "number", "default": 18, "minimum": 0, "maximum": 23, "description": "Hour for scheduled commit"}, "autocommit.scheduledMinute": {"type": "number", "default": 0, "minimum": 0, "maximum": 59, "description": "Minute for scheduled commit"}, "autocommit.scheduledSecond": {"type": "number", "default": 0, "minimum": 0, "maximum": 59, "description": "Second for scheduled commit"}, "autocommit.intervalEnabled": {"type": "boolean", "default": false, "description": "Enable interval commits"}, "autocommit.intervalHours": {"type": "number", "default": 1, "minimum": 0, "maximum": 23, "description": "Hours for interval commits"}, "autocommit.intervalMinutes": {"type": "number", "default": 0, "minimum": 0, "maximum": 59, "description": "Minutes for interval commits"}, "autocommit.intervalSeconds": {"type": "number", "default": 0, "minimum": 0, "maximum": 59, "description": "Seconds for interval commits"}}}}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "tsc -p ./", "watch": "tsc -watch -p ./", "package": "vsce package", "publish": "vsce publish"}, "devDependencies": {"@types/vscode": "^1.74.0", "@types/node": "16.x", "typescript": "^4.9.4"}, "dependencies": {"simple-git": "^3.19.1"}}