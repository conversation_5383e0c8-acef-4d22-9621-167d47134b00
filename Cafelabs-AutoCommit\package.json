{"name": "cafelabs-autocommit", "displayName": "CafeLabs AutoCommit", "description": "Automatic Git commit with customizable timing", "version": "1.0.0", "engines": {"vscode": "^1.74.0"}, "categories": ["Other"], "activationEvents": ["onStartupFinished"], "main": "./out/extension.js", "contributes": {"viewsContainers": {"activitybar": [{"id": "autocommit", "title": "AutoCommit", "icon": "$(git-commit)"}]}, "views": {"autocommit": [{"id": "autocommitSettings", "name": "Settings", "type": "webview"}]}, "commands": [{"command": "autocommit.enable", "title": "Enable AutoCommit"}, {"command": "autocommit.disable", "title": "Disable AutoCommit"}, {"command": "autocommit.manualCommit", "title": "Manual Commit"}]}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "tsc -p ./", "watch": "tsc -watch -p ./"}, "devDependencies": {"@types/vscode": "^1.74.0", "@types/node": "16.x", "typescript": "^4.9.4"}, "dependencies": {"simple-git": "^3.19.1"}}