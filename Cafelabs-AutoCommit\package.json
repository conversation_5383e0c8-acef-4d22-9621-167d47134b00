{"name": "cafelabs-autocommit-minimal", "displayName": "CafeLabs AutoCommit Minimal", "description": "Minimal test version of AutoCommit plugin", "version": "1.0.7", "publisher": "cafelabs", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/cafelabs/autocommit-vscode.git"}, "engines": {"vscode": "^1.74.0"}, "categories": ["Other"], "activationEvents": ["*"], "main": "./out/minimal-extension.js", "contributes": {"viewsContainers": {"activitybar": [{"id": "autocommit", "title": "AutoCommit Minimal", "icon": "$(git-commit)"}]}, "views": {"autocommit": [{"id": "autocommitSettings", "name": "Minimal Settings", "type": "webview"}]}, "commands": [{"command": "autocommit.test", "title": "AutoCommit: Test Minimal"}, {"command": "autocommit.manualCommit", "title": "AutoCommit: Manual Commit"}, {"command": "autocommit.openSettings", "title": "AutoCommit: Open Settings Panel"}]}, "configuration": {"title": "AutoCommit", "properties": {"autocommit.scheduledEnabled": {"type": "boolean", "default": false, "description": "Enable scheduled commits"}, "autocommit.scheduledHour": {"type": "number", "default": 18, "minimum": 0, "maximum": 23, "description": "Hour for scheduled commit"}, "autocommit.scheduledMinute": {"type": "number", "default": 0, "minimum": 0, "maximum": 59, "description": "Minute for scheduled commit"}, "autocommit.scheduledSecond": {"type": "number", "default": 0, "minimum": 0, "maximum": 59, "description": "Second for scheduled commit"}, "autocommit.intervalEnabled": {"type": "boolean", "default": false, "description": "Enable interval commits"}, "autocommit.intervalHours": {"type": "number", "default": 0, "minimum": 0, "maximum": 23, "description": "Hours for interval commits"}, "autocommit.intervalMinutes": {"type": "number", "default": 30, "minimum": 0, "maximum": 59, "description": "Minutes for interval commits"}, "autocommit.intervalSeconds": {"type": "number", "default": 0, "minimum": 0, "maximum": 59, "description": "Seconds for interval commits"}}}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "tsc -p ./", "watch": "tsc -watch -p ./"}, "devDependencies": {"@types/vscode": "^1.74.0", "@types/node": "16.x", "typescript": "^4.9.4"}}