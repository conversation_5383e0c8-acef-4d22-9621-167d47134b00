{"version": 3, "file": "gitManager.js", "sourceRoot": "", "sources": ["../src/gitManager.ts"], "names": [], "mappings": ";;;AAAA,iCAAiC;AACjC,2CAAkD;AAQlD,MAAa,UAAU;IAInB;QAHQ,QAAG,GAAqB,IAAI,CAAC;QAC7B,kBAAa,GAAkB,IAAI,CAAC;QAGxC,IAAI,CAAC,aAAa,EAAE,CAAC;IACzB,CAAC;IAEO,aAAa;QACjB,IAAI,MAAM,CAAC,SAAS,CAAC,gBAAgB,IAAI,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE;YACnF,IAAI,CAAC,aAAa,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC;YACrE,IAAI,CAAC,GAAG,GAAG,IAAA,sBAAS,EAAC,IAAI,CAAC,aAAa,CAAC,CAAC;SAC5C;IACL,CAAC;IAED,KAAK,CAAC,kBAAkB;QACpB,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;YAClC,OAAO,KAAK,CAAC;SAChB;QAED,IAAI;YACA,MAAM,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC;YACxB,OAAO,IAAI,CAAC;SACf;QAAC,OAAO,KAAK,EAAE;YACZ,OAAO,CAAC,GAAG,CAAC,uBAAuB,EAAE,KAAK,CAAC,CAAC;YAC5C,OAAO,KAAK,CAAC;SAChB;IACL,CAAC;IAED,KAAK,CAAC,UAAU;QACZ,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE;YACX,OAAO,KAAK,CAAC;SAChB;QAED,IAAI;YACA,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC;YACvC,OAAO,MAAM,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;SAClC;QAAC,OAAO,KAAK,EAAE;YACZ,OAAO,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;YACnD,OAAO,KAAK,CAAC;SAChB;IACL,CAAC;IAED,KAAK,CAAC,aAAa,CAAC,aAAsB;QACtC,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE;YACX,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,qBAAqB,EAAE,CAAC;SAC7D;QAED,IAAI;YACA,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;YAC3C,IAAI,CAAC,UAAU,EAAE;gBACb,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,sBAAsB,EAAE,CAAC;aAC9D;YAED,kBAAkB;YAClB,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;YAExB,0BAA0B;YAC1B,MAAM,OAAO,GAAG,aAAa,IAAI,IAAI,CAAC,qBAAqB,EAAE,CAAC;YAE9D,iBAAiB;YACjB,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;YAE9C,OAAO;gBACH,OAAO,EAAE,IAAI;gBACb,OAAO,EAAE,cAAc,OAAO,KAAK,MAAM,CAAC,MAAM,GAAG;aACtD,CAAC;SACL;QAAC,OAAO,KAAK,EAAE;YACZ,OAAO,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;YAClD,OAAO;gBACH,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,kBAAkB,KAAK,EAAE;aACrC,CAAC;SACL;IACL,CAAC;IAEO,qBAAqB;QACzB,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;QACvB,MAAM,SAAS,GAAG,GAAG,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QACvE,OAAO,gBAAgB,SAAS,EAAE,CAAC;IACvC,CAAC;IAED,KAAK,CAAC,iBAAiB;QACnB,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE;YACX,OAAO,qBAAqB,CAAC;SAChC;QAED,IAAI;YACA,MAAM,GAAG,GAAG,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE,CAAC,CAAC;YAChD,IAAI,GAAG,CAAC,MAAM,EAAE;gBACZ,MAAM,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC;gBAC1B,OAAO,GAAG,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,MAAM,MAAM,CAAC,OAAO,KAAK,MAAM,CAAC,IAAI,GAAG,CAAC;aAChF;YACD,OAAO,kBAAkB,CAAC;SAC7B;QAAC,OAAO,KAAK,EAAE;YACZ,OAAO,UAAU,KAAK,EAAE,CAAC;SAC5B;IACL,CAAC;CACJ;AAlGD,gCAkGC"}