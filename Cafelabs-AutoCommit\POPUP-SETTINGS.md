# AutoCommit 弹窗设置面板功能

## 🎉 新功能 - 独立设置窗口

### 📦 版本信息
**文件**: `cafelabs-autocommit-minimal-1.0.7.vsix`

### 🚀 弹窗设置面板功能

#### 1. **新增命令**
- **命令**: `AutoCommit: Open Settings Panel`
- **功能**: 打开独立的AutoCommit设置窗口
- **调用方式**: 
  - 命令面板: `Ctrl+Shift+P` → "AutoCommit: Open Settings Panel"
  - 可以绑定快捷键或添加到工具栏

#### 2. **独立设置窗口特点**
- ✅ **独立窗口**: 在编辑器中打开新的标签页
- ✅ **完整功能**: 包含所有AutoCommit设置选项
- ✅ **实时保存**: 设置自动保存到VSCode配置
- ✅ **美观界面**: 专门设计的大窗口界面
- ✅ **实时时钟**: 显示当前时间
- ✅ **双分支提交**: 包含手工提交功能

#### 3. **界面内容**
```
🚀 AutoCommit Settings Panel
├── 🕐 Scheduled Commit
│   ├── ☑️ Enable scheduled commit
│   └── ⏰ Time: HH:MM:SS (24-hour format)
├── 🔄 Interval Commit  
│   ├── ☑️ Enable interval commit
│   └── ⏱️ Every: X hours Y minutes Z seconds
├── 📋 Dual Branch Commit Info
│   └── 说明双分支提交功能
└── ⚡ Control Buttons
    ├── 💾 Save Settings
    ├── 📥 Load Settings
    ├── ▶️ Enable AutoCommit
    ├── ⏹️ Disable AutoCommit
    ├── 🚀 Manual Commit (Dual Branch)
    └── 🧪 Test Function
```

### 🔧 如何添加到Customize Layout前面

#### 方法1: 通过VSCode设置添加工具栏按钮

1. **打开设置**: `Ctrl+,`
2. **搜索**: "toolbar" 或 "customization"
3. **找到**: "Workbench › Editor › Custom Actions"
4. **添加自定义动作**:
```json
{
  "workbench.editor.customActions": [
    {
      "id": "autocommit.openSettings",
      "title": "AutoCommit Settings",
      "icon": "$(gear)",
      "command": "autocommit.openSettings"
    }
  ]
}
```

#### 方法2: 通过键盘快捷键

1. **打开快捷键设置**: `Ctrl+K Ctrl+S`
2. **搜索**: "AutoCommit: Open Settings Panel"
3. **设置快捷键**: 例如 `Ctrl+Alt+A`

#### 方法3: 通过命令面板快速访问

1. **打开命令面板**: `Ctrl+Shift+P`
2. **输入**: "AutoCommit: Open Settings Panel"
3. **执行命令**

#### 方法4: 添加到状态栏 (已实现)

插件已经在状态栏添加了AutoCommit图标，点击即可快速访问。

### 📋 设置持久化

#### 自动保存功能
- ✅ **点击保存**: 手动保存设置到VSCode配置
- ✅ **自动加载**: 打开窗口时自动加载已保存的设置
- ✅ **全局配置**: 设置保存在VSCode全局配置中
- ✅ **跨项目**: 设置在所有项目中生效

#### 配置项目
```json
{
  "autocommit.scheduledEnabled": false,
  "autocommit.scheduledHour": 18,
  "autocommit.scheduledMinute": 0,
  "autocommit.scheduledSecond": 0,
  "autocommit.intervalEnabled": false,
  "autocommit.intervalHours": 0,
  "autocommit.intervalMinutes": 30,
  "autocommit.intervalSeconds": 0
}
```

### 🎯 使用流程

#### 1. **打开设置面板**
```bash
方式1: Ctrl+Shift+P → "AutoCommit: Open Settings Panel"
方式2: 点击状态栏AutoCommit图标
方式3: 使用自定义快捷键
```

#### 2. **配置设置**
```bash
1. 勾选启用定时提交/间隔提交
2. 设置时间参数
3. 点击"💾 Save Settings"保存
4. 设置自动保存到VSCode配置
```

#### 3. **使用功能**
```bash
1. 点击"▶️ Enable AutoCommit"启用
2. 点击"🚀 Manual Commit"手工提交
3. 查看状态更新和通知
```

### 🔍 与侧边栏面板的区别

| 功能 | 侧边栏面板 | 弹窗设置面板 |
|------|------------|--------------|
| 显示位置 | 左侧活动栏 | 编辑器标签页 |
| 窗口大小 | 固定宽度 | 可调整大小 |
| 界面设计 | 紧凑型 | 宽敞美观 |
| 设置保存 | 临时 | 持久化 |
| 实时时钟 | 无 | 有 |
| 详细说明 | 简单 | 详细 |
| 使用场景 | 快速操作 | 详细配置 |

### 🎉 优势特点

1. **更大空间**: 独立窗口提供更多配置空间
2. **更好体验**: 专门设计的美观界面
3. **设置持久**: 配置自动保存，重启后仍然有效
4. **功能完整**: 包含所有AutoCommit功能
5. **灵活调用**: 多种方式打开设置面板
6. **实时反馈**: 显示当前时间和操作状态

### 🚀 立即体验

1. **安装**: `cafelabs-autocommit-minimal-1.0.7.vsix`
2. **打开**: `Ctrl+Shift+P` → "AutoCommit: Open Settings Panel"
3. **配置**: 设置你的提交偏好
4. **保存**: 点击"💾 Save Settings"
5. **使用**: 享受自动化Git提交功能！

现在你有了两种方式来配置AutoCommit：
- **侧边栏面板**: 快速操作和监控
- **弹窗设置面板**: 详细配置和管理

选择最适合你工作流程的方式！
