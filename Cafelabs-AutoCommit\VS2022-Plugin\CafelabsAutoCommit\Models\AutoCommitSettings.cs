using System;

namespace CafelabsAutoCommit.Models
{
    /// <summary>
    /// AutoCommit设置模型
    /// </summary>
    public class AutoCommitSettings
    {
        /// <summary>
        /// 是否启用定时提交
        /// </summary>
        public bool ScheduledEnabled { get; set; } = false;

        /// <summary>
        /// 定时提交的小时 (0-23)
        /// </summary>
        public int ScheduledHour { get; set; } = 18;

        /// <summary>
        /// 定时提交的分钟 (0-59)
        /// </summary>
        public int ScheduledMinute { get; set; } = 0;

        /// <summary>
        /// 定时提交的秒 (0-59)
        /// </summary>
        public int ScheduledSecond { get; set; } = 0;

        /// <summary>
        /// 是否启用间隔提交
        /// </summary>
        public bool IntervalEnabled { get; set; } = false;

        /// <summary>
        /// 间隔提交的小时数 (0-23)
        /// </summary>
        public int IntervalHours { get; set; } = 0;

        /// <summary>
        /// 间隔提交的分钟数 (0-59)
        /// </summary>
        public int IntervalMinutes { get; set; } = 30;

        /// <summary>
        /// 间隔提交的秒数 (0-59)
        /// </summary>
        public int IntervalSeconds { get; set; } = 0;

        /// <summary>
        /// 获取定时提交的时间
        /// </summary>
        public TimeSpan ScheduledTime => new TimeSpan(ScheduledHour, ScheduledMinute, ScheduledSecond);

        /// <summary>
        /// 获取间隔提交的时间间隔
        /// </summary>
        public TimeSpan IntervalTime => new TimeSpan(IntervalHours, IntervalMinutes, IntervalSeconds);

        /// <summary>
        /// 克隆设置对象
        /// </summary>
        public AutoCommitSettings Clone()
        {
            return new AutoCommitSettings
            {
                ScheduledEnabled = this.ScheduledEnabled,
                ScheduledHour = this.ScheduledHour,
                ScheduledMinute = this.ScheduledMinute,
                ScheduledSecond = this.ScheduledSecond,
                IntervalEnabled = this.IntervalEnabled,
                IntervalHours = this.IntervalHours,
                IntervalMinutes = this.IntervalMinutes,
                IntervalSeconds = this.IntervalSeconds
            };
        }

        /// <summary>
        /// 验证设置是否有效
        /// </summary>
        public bool IsValid()
        {
            return ScheduledHour >= 0 && ScheduledHour <= 23 &&
                   ScheduledMinute >= 0 && ScheduledMinute <= 59 &&
                   ScheduledSecond >= 0 && ScheduledSecond <= 59 &&
                   IntervalHours >= 0 && IntervalHours <= 23 &&
                   IntervalMinutes >= 0 && IntervalMinutes <= 59 &&
                   IntervalSeconds >= 0 && IntervalSeconds <= 59;
        }

        /// <summary>
        /// 获取设置描述
        /// </summary>
        public string GetDescription()
        {
            var parts = new System.Collections.Generic.List<string>();
            
            if (ScheduledEnabled)
            {
                parts.Add($"定时提交: {ScheduledTime:hh\\:mm\\:ss}");
            }
            
            if (IntervalEnabled)
            {
                parts.Add($"间隔提交: 每{IntervalTime:hh\\:mm\\:ss}");
            }
            
            return parts.Count > 0 ? string.Join(", ", parts) : "未启用自动提交";
        }
    }
}
