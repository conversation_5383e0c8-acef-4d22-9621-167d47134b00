<Window x:Class="CafelabsAutoCommit.UI.SettingsDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d"
        Title="🔧 CafeLabs AutoCommit 设置面板" 
        Height="650" 
        Width="500"
        WindowStartupLocation="CenterScreen"
        ResizeMode="CanResize"
        Background="{DynamicResource {x:Static SystemColors.WindowBrushKey}}">
    
    <Window.Resources>
        <Style x:Key="HeaderStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="18"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="#4CAF50"/>
            <Setter Property="Margin" Value="0,15,0,10"/>
        </Style>
        
        <Style x:Key="SectionStyle" TargetType="GroupBox">
            <Setter Property="BorderBrush" Value="#4CAF50"/>
            <Setter Property="BorderThickness" Value="2"/>
            <Setter Property="Padding" Value="15"/>
            <Setter Property="Margin" Value="0,10"/>
            <Setter Property="Background" Value="#F8F9FA"/>
        </Style>
        
        <Style x:Key="ButtonStyle" TargetType="Button">
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="MinWidth" Value="100"/>
        </Style>
        
        <Style x:Key="PrimaryButtonStyle" TargetType="Button" BasedOn="{StaticResource ButtonStyle}">
            <Setter Property="Background" Value="#4CAF50"/>
            <Setter Property="Foreground" Value="White"/>
        </Style>
        
        <Style x:Key="SecondaryButtonStyle" TargetType="Button" BasedOn="{StaticResource ButtonStyle}">
            <Setter Property="Background" Value="#FF6B35"/>
            <Setter Property="Foreground" Value="White"/>
        </Style>
    </Window.Resources>

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题区域 -->
        <StackPanel Grid.Row="0">
            <TextBlock Text="🚀 CafeLabs AutoCommit 设置面板" 
                       FontSize="24" 
                       FontWeight="Bold" 
                       HorizontalAlignment="Center" 
                       Foreground="#4CAF50" 
                       Margin="0,0,0,10"/>
            <TextBlock Text="配置自动提交参数和双分支提交设置" 
                       FontSize="14" 
                       HorizontalAlignment="Center" 
                       Foreground="Gray" 
                       Margin="0,0,0,20"/>
        </StackPanel>

        <!-- 设置内容区域 -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel>
                <!-- 定时提交设置 -->
                <GroupBox Header="🕐 定时提交设置" Style="{StaticResource SectionStyle}">
                    <StackPanel>
                        <CheckBox x:Name="ScheduledEnabledCheckBox" 
                                  Content="启用定时提交 (每日指定时间自动提交)" 
                                  FontSize="14"
                                  Margin="0,10"
                                  Checked="OnSettingChanged"
                                  Unchecked="OnSettingChanged"/>
                        
                        <StackPanel Orientation="Horizontal" Margin="0,10">
                            <TextBlock Text="提交时间:" 
                                       VerticalAlignment="Center" 
                                       FontWeight="Bold"
                                       Margin="0,0,15,0"/>
                            <TextBox x:Name="ScheduledHourTextBox" 
                                     Width="50" 
                                     Text="18" 
                                     TextAlignment="Center"
                                     FontSize="14"
                                     TextChanged="OnSettingChanged"/>
                            <TextBlock Text="时" VerticalAlignment="Center" Margin="5,0,10,0"/>
                            <TextBox x:Name="ScheduledMinuteTextBox" 
                                     Width="50" 
                                     Text="0" 
                                     TextAlignment="Center"
                                     FontSize="14"
                                     TextChanged="OnSettingChanged"/>
                            <TextBlock Text="分" VerticalAlignment="Center" Margin="5,0,10,0"/>
                            <TextBox x:Name="ScheduledSecondTextBox" 
                                     Width="50" 
                                     Text="0" 
                                     TextAlignment="Center"
                                     FontSize="14"
                                     TextChanged="OnSettingChanged"/>
                            <TextBlock Text="秒" VerticalAlignment="Center" Margin="5,0"/>
                        </StackPanel>
                        
                        <TextBlock Text="💡 提示: 使用24小时制，例如 18:30:00 表示下午6点30分" 
                                   FontSize="12" 
                                   Foreground="Gray" 
                                   Margin="0,5"/>
                    </StackPanel>
                </GroupBox>

                <!-- 间隔提交设置 -->
                <GroupBox Header="🔄 间隔提交设置" Style="{StaticResource SectionStyle}">
                    <StackPanel>
                        <CheckBox x:Name="IntervalEnabledCheckBox" 
                                  Content="启用间隔提交 (按指定时间间隔重复提交)" 
                                  FontSize="14"
                                  Margin="0,10"
                                  Checked="OnSettingChanged"
                                  Unchecked="OnSettingChanged"/>
                        
                        <StackPanel Orientation="Horizontal" Margin="0,10">
                            <TextBlock Text="时间间隔:" 
                                       VerticalAlignment="Center" 
                                       FontWeight="Bold"
                                       Margin="0,0,15,0"/>
                            <TextBox x:Name="IntervalHoursTextBox" 
                                     Width="50" 
                                     Text="0" 
                                     TextAlignment="Center"
                                     FontSize="14"
                                     TextChanged="OnSettingChanged"/>
                            <TextBlock Text="小时" VerticalAlignment="Center" Margin="5,0,10,0"/>
                            <TextBox x:Name="IntervalMinutesTextBox" 
                                     Width="50" 
                                     Text="30" 
                                     TextAlignment="Center"
                                     FontSize="14"
                                     TextChanged="OnSettingChanged"/>
                            <TextBlock Text="分钟" VerticalAlignment="Center" Margin="5,0,10,0"/>
                            <TextBox x:Name="IntervalSecondsTextBox" 
                                     Width="50" 
                                     Text="0" 
                                     TextAlignment="Center"
                                     FontSize="14"
                                     TextChanged="OnSettingChanged"/>
                            <TextBlock Text="秒" VerticalAlignment="Center" Margin="5,0"/>
                        </StackPanel>
                        
                        <TextBlock Text="💡 提示: 最小间隔建议不少于5分钟，避免频繁提交" 
                                   FontSize="12" 
                                   Foreground="Gray" 
                                   Margin="0,5"/>
                    </StackPanel>
                </GroupBox>

                <!-- 双分支提交信息 -->
                <GroupBox Header="📋 双分支提交说明" Style="{StaticResource SectionStyle}">
                    <StackPanel>
                        <TextBlock Text="🎯 功能特性:" FontWeight="Bold" Margin="0,5"/>
                        <TextBlock Text="• 自动提交到当前工作分支" FontSize="12" Margin="15,2"/>
                        <TextBlock Text="• 同时提交到 cafelabs-autoCommit 分支" FontSize="12" Margin="15,2"/>
                        <TextBlock Text="• 自动创建目标分支（如果不存在）" FontSize="12" Margin="15,2"/>
                        <TextBlock Text="• 智能分支切换和错误恢复" FontSize="12" Margin="15,2"/>
                        <TextBlock Text="• 冲突检测和安全回滚" FontSize="12" Margin="15,2"/>
                        
                        <TextBlock Text="⚠️ 注意事项:" FontWeight="Bold" Margin="0,10,0,5"/>
                        <TextBlock Text="• 确保工作区有Git仓库" FontSize="12" Margin="15,2"/>
                        <TextBlock Text="• 建议在提交前检查代码状态" FontSize="12" Margin="15,2"/>
                        <TextBlock Text="• 大型项目建议适当调整提交频率" FontSize="12" Margin="15,2"/>
                    </StackPanel>
                </GroupBox>

                <!-- 状态显示 -->
                <GroupBox Header="📊 当前状态" Style="{StaticResource SectionStyle}">
                    <StackPanel>
                        <TextBlock x:Name="StatusText" 
                                   Text="✅ 设置面板已加载" 
                                   FontSize="14"
                                   TextWrapping="Wrap"
                                   Margin="0,5"/>
                        <TextBlock x:Name="PreviewText" 
                                   Text="预览: 当前设置将在保存后生效" 
                                   FontSize="12"
                                   Foreground="Gray"
                                   TextWrapping="Wrap"
                                   Margin="0,5"/>
                    </StackPanel>
                </GroupBox>
            </StackPanel>
        </ScrollViewer>

        <!-- 按钮区域 -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,20,0,0">
            <Button x:Name="SaveButton" 
                    Content="💾 保存设置" 
                    Style="{StaticResource PrimaryButtonStyle}"
                    Click="OnSaveClick"/>
            <Button x:Name="CancelButton" 
                    Content="❌ 取消" 
                    Style="{StaticResource ButtonStyle}"
                    Click="OnCancelClick"/>
            <Button x:Name="ResetButton" 
                    Content="🔄 重置" 
                    Style="{StaticResource SecondaryButtonStyle}"
                    Click="OnResetClick"/>
            <Button x:Name="TestButton" 
                    Content="🧪 测试" 
                    Style="{StaticResource ButtonStyle}"
                    Click="OnTestClick"/>
        </StackPanel>
    </Grid>
</Window>
